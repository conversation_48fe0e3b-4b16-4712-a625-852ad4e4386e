package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.LineDashStyleType;
import com.spire.presentation.charts.ErrorBarSimpleType;
import com.spire.presentation.charts.ErrorValueType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.IErrorBarsFormat;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;
import java.util.Iterator;

public class OperatioErrorBarTest {

    @Test
    public void testRead() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-散点图2.pptx");
        IChart chart = prismaPptUtil.getChart("scatter-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
//            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        System.out.println(chart.getPrimaryValueAxis().getTitle().getTextProperties().getText());
        System.out.println(chart.getSecondaryValueAxis().getTitle().getTextProperties().getText());
        int seriesCount = chart.getSeries().size();
        for (int i = 0; i < seriesCount; i ++) {
            ChartSeriesDataFormat series = chart.getSeries().get(i);
            if (series.getNamedRange().getCount() > 0) {
                System.out.println(series.getNamedRange().get(0).getText());
            }
            if (chart.getSeries().getSeriesLabel().getCount() > 0) {
                System.out.println(chart.getSeries().getSeriesLabel().get(0).getText());
            }
            int xValuesCount = series.getXValues().getCount();
            for (int j =0;j < xValuesCount; j ++) {
                CellRange cellRange = series.getXValues().get(j);
                System.out.println("xAxis=" + cellRange.getText());
//                cellRange.setNumberValue(Integer.valueOf((String) cellRange.getValue()) + 5);
            }
            int yValuesCount = series.getYValues().getCount();
            for (int j =0; j < yValuesCount; j ++) {
                CellRange cellRange = series.getYValues().get(j);
                System.out.println("yAxis=" +cellRange.getText());
//                cellRange.setNumberValue(Integer.valueOf((String) cellRange.getValue()) + 5);
            }
            int dataLabelsCount = series.getDataLabels().getCount();
            for (int j = 0; j < dataLabelsCount; j ++) {
                System.out.println(series.getDataLabels().get(j).hasDataSource());
//                CellRange cellRange = (CellRange) series.getDataLabels().get(j).getTextFrame().getTextRange().getText();
//                CellRange cellRange1 = new CellRange("");
                System.out.println(series.getBubbles().getCount());
                System.out.println(series.getDataLabels().getPosition().getName());
                System.out.println(series.getDataLabels().getPosition().getValue());
                System.out.println(series.getDataLabels().get(j).getTextFrame().getTextRange().getField().getType().getClass());
//                System.out.println(series.getDataLabels().get(j).getTextProperties().getTextRange().getField().getType());
                System.out.println(series.getDataLabels().get(j).getTextFrame().getTextRange().getText());
                System.out.println(series.getDataLabels().get(j).getTextFrame().getTextRange().getTextCapType());
                System.out.println(series.getDataLabels().get(j).getDataLabelSize());
//                System.out.println(series.ge);
//                series.getDataLabels().get(j).set

            }
            System.out.println(chart.getChartData().get("B1").getText());
            System.out.println(chart.getChartData().get("B2").getText());
            System.out.println(chart.getChartData().get("B3").getText());
        }
        //保存文件
//        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-柱状图3.pptx");
    }

    private DataTable getDataTable() throws Exception {
        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("常模切分区域", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("序号", DataTypes.DATATABLE_INT));
        dataTable.getColumns().add(new DataColumn("敬业度指数", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("组织能力指数", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("分析主体", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("常模", DataTypes.DATATABLE_DOUBLE));

        DataRow row1 = dataTable.newRow();
        row1.setString("常模切分区域", "风险区2");
        row1.setInt("序号", 1);
        row1.setDouble("敬业度指数", 30.3);
        row1.setDouble("组织能力指数", 78.5);
        row1.setDouble("分析主体", 50.5);
        row1.setDouble("常模", 60.5);
        DataRow row2 = dataTable.newRow();
        row2.setString("常模切分区域", "疲惫区2");
        row2.setInt("序号", 2);
        row2.setDouble("敬业度指数", 36.8);
        row2.setDouble("组织能力指数", 34.3);
        row2.setDouble("分析主体", 50.5);
        row2.setDouble("常模", 40.5);
        DataRow row3 = dataTable.newRow();
        row3.setString("常模切分区域", "疲惫区");
        row3.setInt("序号", 3);
        row3.setDouble("敬业度指数", 80.9);
        row3.setDouble("组织能力指数", 34.1);
        DataRow row4 = dataTable.newRow();
        row4.setString("常模切分区域", "疲惫区");
        row4.setInt("序号", 4);
        row4.setDouble("敬业度指数", 88.9);
        row4.setDouble("组织能力指数", 77.9);
        DataRow row5 = dataTable.newRow();
        row5.setString("常模切分区域", "怠慢区");
        row5.setInt("序号", 5);
        row5.setDouble("敬业度指数", 50.3);
        row5.setDouble("组织能力指数", 48.7);
        DataRow row6 = dataTable.newRow();
        row6.setString("常模切分区域", "活力区");
        row6.setInt("序号", 6);
        row6.setDouble("敬业度指数", 29.4);
        row6.setDouble("组织能力指数", 66.7);
        DataRow row7 = dataTable.newRow();
        row7.setString("常模切分区域", "活力区");
        row7.setInt("序号", 7);
        row7.setDouble("敬业度指数", 28.4);
        row7.setDouble("组织能力指数", 60.7);
        dataTable.getRows().add(row1);
        dataTable.getRows().add(row2);
        dataTable.getRows().add(row3);
        dataTable.getRows().add(row4);
        dataTable.getRows().add(row5);
        dataTable.getRows().add(row6);
        dataTable.getRows().add(row7);
        return dataTable;

    }


    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-散点图2.pptx");
        IChart chart = prismaPptUtil.getChart("scatter-chart");
        DataTable dataTable = getDataTable();
        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }

        //设置类别标签
//        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A5"));

        //设置系列标签
//        chart.getSeries().getSeriesLabel().get(0).setText("");
//        chart.getSeries().getSeriesLabel().get(1).setText();
//        chart.getSeries().getSeriesLabel().get(2).setText("");
        chart.getSeries().setSeriesLabel(chart.getChartData().get("D1", "F1"));
//        chart.getSeries().getSeriesLabel().get(0).setText("分析主体");
//        chart.getSeries().getSeriesLabel().get(1).setText("常模");
//        chart.getSeries().getSeriesLabel().get(2).setText("部门");


        //为各个系列赋值
        // 散点
        chart.getSeries().get(0).setXValues(chart.getChartData().get("D2", "D8"));
        chart.getSeries().get(0).setYValues(chart.getChartData().get("C2", "C8"));

        // 误差线 分析主体
        chart.getSeries().get(1).setXValues(chart.getChartData().get("E2", "E2"));
        chart.getSeries().get(1).setYValues(chart.getChartData().get("E3", "E3"));
//        chart.getSeries().get(0).setUseSecondAxis(true);

        IErrorBarsFormat xErrorBarsFormat = chart.getSeries().get(1).getErrorBarsXFormat();
        xErrorBarsFormat.setErrorBarvType(ErrorValueType.CUSTOM_ERROR_BARS.getValue());
        xErrorBarsFormat.setErrorBarSimType(ErrorBarSimpleType.BOTH.getValue());
        xErrorBarsFormat.setErrorBarNoEndCap(true);
        xErrorBarsFormat.setPlusVal((Float) chart.getChartData().get("E3", "E3").get(0).getValue());
        xErrorBarsFormat.setMinusVal((Float) chart.getChartData().get("E2", "E2").get(0).getValue());
//        xErrorBarsFormat.setErrorBarVal(50.5f);
//        xErrorBarsFormat.getLine().setCapStyle(LineCapStyle.FLAT);
        xErrorBarsFormat.getLine().setDashStyle(LineDashStyleType.DASH);
        xErrorBarsFormat.getLine().setWidth(1);
        xErrorBarsFormat.getLine().setFillType(FillFormatType.SOLID);
        xErrorBarsFormat.getLine().getSolidFillColor().setColor(Color.ORANGE);



        IErrorBarsFormat yErrorBarsFormat = chart.getSeries().get(1).getErrorBarsYFormat();
        yErrorBarsFormat.getLine().setDashStyle(LineDashStyleType.DASH);
        yErrorBarsFormat.getLine().setFillType(FillFormatType.SOLID);
        yErrorBarsFormat.getLine().getSolidFillColor().setColor(Color.ORANGE);
//        yErrorBarsFormat.getLine().setLineBeginWidth(LineEndWidth.NONE);
//        yErrorBarsFormat.getLine().setLineEndWidth(LineEndWidth.NONE);
        yErrorBarsFormat.getLine().setWidth(1);
        yErrorBarsFormat.setErrorBarvType(ErrorValueType.CUSTOM_ERROR_BARS.getValue());
        yErrorBarsFormat.setErrorBarSimType(ErrorBarSimpleType.BOTH.getValue());
        yErrorBarsFormat.setErrorBarNoEndCap(true);
//        yErrorBarsFormat.setPlusVal(50.5f);
//        yErrorBarsFormat.setMinusVal(50.5f);
        yErrorBarsFormat.setPlusVal((Float) chart.getChartData().get("E2", "E2").get(0).getValue());
        yErrorBarsFormat.setMinusVal((Float) chart.getChartData().get("E3", "E3").get(0).getValue());
//        yErrorBarsFormat.setErrorBarVal(50.5f);



        // 误差线 常模
        chart.getSeries().get(2).setXValues(chart.getChartData().get("F2", "F2"));
        chart.getSeries().get(2).setYValues(chart.getChartData().get("F3", "F3"));
//        chart.getSeries().get(1).setUseSecondAxis(true);
        IErrorBarsFormat xErrorBarsFormat2 = chart.getSeries().get(2).getErrorBarsXFormat();
        xErrorBarsFormat2.getLine().setDashStyle(LineDashStyleType.DASH);
        xErrorBarsFormat2.getLine().setFillType(FillFormatType.SOLID);
        xErrorBarsFormat2.getLine().getSolidFillColor().setColor(Color.GRAY);
//        xErrorBarsFormat.getLine().setLineBeginWidth(LineEndWidth.NONE);
//        xErrorBarsFormat.getLine().setLineEndWidth(LineEndWidth.NONE);
        xErrorBarsFormat2.getLine().setWidth(1);
        xErrorBarsFormat2.setErrorBarvType(ErrorValueType.CUSTOM_ERROR_BARS.getValue());
        xErrorBarsFormat2.setErrorBarSimType(ErrorBarSimpleType.BOTH.getValue());
        xErrorBarsFormat2.setErrorBarNoEndCap(true);
        xErrorBarsFormat2.setPlusVal((Float) chart.getChartData().get("F3", "F3").get(0).getValue());
        xErrorBarsFormat2.setMinusVal((Float) chart.getChartData().get("F2", "F2").get(0).getValue());
//        xErrorBarsFormat2.setErrorBarVal(5);


        IErrorBarsFormat yErrorBarsFormat2 = chart.getSeries().get(2).getErrorBarsYFormat();
        yErrorBarsFormat2.getLine().setDashStyle(LineDashStyleType.DASH);
        yErrorBarsFormat2.getLine().setFillType(FillFormatType.SOLID);
        yErrorBarsFormat2.getLine().getSolidFillColor().setColor(Color.GRAY);
//        yErrorBarsFormat.getLine().setLineBeginWidth(LineEndWidth.NONE);
//        yErrorBarsFormat.getLine().setLineEndWidth(LineEndWidth.NONE);
        yErrorBarsFormat2.getLine().setWidth(1);
        yErrorBarsFormat2.setErrorBarvType(ErrorValueType.CUSTOM_ERROR_BARS.getValue());
        yErrorBarsFormat2.setErrorBarSimType(ErrorBarSimpleType.BOTH.getValue());
        yErrorBarsFormat2.setErrorBarNoEndCap(true);
        yErrorBarsFormat2.setPlusVal((Float) chart.getChartData().get("F2", "F2").get(0).getValue());
        yErrorBarsFormat2.setMinusVal((Float) chart.getChartData().get("F3", "F3").get(0).getValue());




        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-散点图2.pptx");
    }

}
