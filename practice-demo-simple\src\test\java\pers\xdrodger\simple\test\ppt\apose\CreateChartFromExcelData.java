package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.FileFormat;
import com.spire.presentation.Presentation;
import com.spire.presentation.SlideSizeType;
import com.spire.presentation.charts.ChartStyle;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.xls.Workbook;
import com.spire.xls.Worksheet;
import org.junit.Test;

import java.awt.geom.Rectangle2D;

public class CreateChartFromExcelData {

    String fileName = "";

    private boolean isWindows() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("windows")) {
            return true;
        }
        return false;
    }


    private String getInputFilePath() {
        if (isWindows()) {
            return "D:\\workspace\\practice-demo\\file\\input\\";
        }
        return "/Library/workspace/my-project/practice-demo/file/input/";
    }

    private String getOutputFilePath() {
        if (isWindows()) {
            return "D:\\workspace\\practice-demo\\file\\output\\";
        }
        return "/Library/workspace/my-project/practice-demo/file/output/";
    }

    @Test
    public void create() throws Exception {
        // establish Presentation object
        Presentation presentation = new Presentation();
        presentation.getSlideSize().setType(SlideSizeType.SCREEN_16_X_9);

        // Add histogram
        Rectangle2D rect = new Rectangle2D.Float(200, 100, 550, 320);
        IChart chart = presentation.getSlides().get(0).getShapes().appendChart(ChartType.COLUMN_CLUSTERED,rect);

        // Clear default chart data
        chart.getChartData().clear(0,0,5,5 );

        // establish Workbook Object and load Excel file
        Workbook wb = new Workbook();
        wb.loadFromFile(getInputFilePath() + "data.xlsx");

        // Get the first sheet
        Worksheet sheet = wb.getWorksheets().get(0);

        // take Excel Import data from chart data table
        for (int r = 0; r < sheet.getAllocatedRange().getRowCount(); r++)
        {
            for (int c = 0; c < sheet.getAllocatedRange().getColumnCount(); c++)
            {
                chart.getChartData().get(r,c).setValue(sheet.getCellRange(r+1, c+1).getValue2());
            }
        }

        // Add the title
        chart.getChartTitle().getTextProperties().setText(" The distribution of male and female members ");
        chart.getChartTitle().getTextProperties().isCentered(true);
        chart.getChartTitle().setHeight(25f);
        chart.hasTitle(true);

        // Set the series label
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1","C1"));

        // Set category labels
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2","A5"));

        // Set series data
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2","B5"));
        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C5"));

        // Apply built-in styles
        chart.setChartStyle(ChartStyle.STYLE_11);

        // Set series overlap
        chart.setOverLap(-50);

        // Set classification spacing
        chart.setGapWidth(200);

        // Save the document
        presentation.saveToFile(getOutputFilePath() + "Chart-CN.pptx", FileFormat.PPTX_2013);
    }
}