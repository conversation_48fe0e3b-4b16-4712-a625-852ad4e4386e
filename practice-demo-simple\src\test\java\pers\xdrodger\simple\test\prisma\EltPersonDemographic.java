package pers.xdrodger.simple.test.prisma;

import lombok.Data;

import java.io.Serializable;

@Data
public class EltPersonDemographic implements Serializable {


    private String personId;
    /**
     * 底层组织id
     */
    private String organizationId;
    /**
     * 分析因子1选项值
     */
    private String c1;
    private String c2;
    private String c3;
    private String c4;
    private String c5;
    private String c6;
    private String c7;
    private String c8;

}
