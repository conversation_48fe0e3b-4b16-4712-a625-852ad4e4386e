package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.graphics.PdfGraphicsUnit;
import com.spire.pdf.graphics.PdfUnitConvertor;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.*;
import com.spire.presentation.charts.ChartDataLabelPosition;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartDataLabel;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.ChartCategorySeriesData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableCellData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableRowData;

import java.math.BigDecimal;
import java.util.*;

public class AdjustApprovalChartTest {


    private Double getRandomDouble() {
        Random random = new Random();
        return new BigDecimal(random.nextDouble() * 100).setScale(1, BigDecimal.ROUND_UP).doubleValue();
    }


    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-赞成度表.pptx");
        List<String> headList = new ArrayList<>();
        headList.add(null);
        headList.add(null);
        headList.add("分析主体2");
        headList.add("射手座上海开发部门组织架构很长");
        headList.add("互联网行业50分位2020年常模数据");
        headList.add("VS20212");
        headList.add("VS2023");
        headList.add("VS2024");
        headList.add("VS2025");
        headList.add("VS2026");

        List<PptTableRowData> rowDataList = new LinkedList<>();
        Random random = new Random();
        random.nextDouble();
        for (int  i = 0; i < 15; i ++) {
            PptTableRowData rowData = new PptTableRowData();
            List<PptTableCellData> cellDataList = new ArrayList<>();
            cellDataList.add(new PptTableCellData("子维度" + i));
            cellDataList.add(new PptTableCellData(""));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            rowData.setCellDataList(cellDataList);
            rowDataList.add(rowData);
        }
        PptTableData pptTableData = new PptTableData();
        pptTableData.setShapeName("eei-new-table");
        pptTableData.setHeadList(headList);
        pptTableData.setBodyList(rowDataList);

        ITable table = prismaPptUtil.getTable("eei-new-table");
        int rowCount = table.getTableRows().getCount();
        int colCount = table.getColumnsList().getCount();
        if (colCount < headList.size()) {
            for (int i = colCount; i < headList.size(); i ++) {
                TableColumn column =table.getColumnsList().get(colCount-1);
                table.getColumnsList().add(column);
            }
        }
        if (rowCount < (1 + rowDataList.size())) {
            for (int i = rowCount; i < (1 + rowDataList.size()); i ++) {
                TableRow row = table.getTableRows().get(rowCount - 1);
                table.getTableRows().append(row);
            }
        }
        rowCount = table.getTableRows().getCount();
        colCount = table.getColumnsList().getCount();
        for (int i = 0; i < rowCount; i ++) {
            for (int j =0; j < colCount; j ++) {
                Cell cell = table.get(j, i);
                if (i == 0) {
                    String value = headList.get(j);
                    if (value == null) {
                        value = "";
                    }
                    cell.getTextFrame().setText(value);
                } else {
                    Object value = rowDataList.get(i -1).getCellDataList().get(j).getValue();
                    if (value == null) {
                        cell.getTextFrame().setText("");
                    } else if (value instanceof Double) {
                        cell.getTextFrame().setText(((Double) value).toString());
                    } else {
                        cell.getTextFrame().setText(((String) value));
                    }
                }

            }
        }

        List<ChartCategorySeriesData> chartSeriesDataList = new ArrayList<>();
        ChartCategorySeriesData categoryData = new ChartCategorySeriesData();
        categoryData.setName("子维度名称");
        List<String> categoryDataList = new ArrayList<>();
        categoryData.setDataList(categoryDataList);
        chartSeriesDataList.add(categoryData);
        ChartCategorySeriesData approvalSeriesData = new ChartCategorySeriesData();
        approvalSeriesData.setName("赞成度2");
        List<Double> approvalDataList = new ArrayList<>();
        approvalSeriesData.setDataList(approvalDataList);
        chartSeriesDataList.add(approvalSeriesData);
        ChartCategorySeriesData neutralSeriesData = new ChartCategorySeriesData();
        neutralSeriesData.setName("中立2");
        List<Double> neutralDataList = new ArrayList<>();
        neutralSeriesData.setDataList(neutralDataList);
        chartSeriesDataList.add(neutralSeriesData);
        ChartCategorySeriesData disapprovalSeriesData = new ChartCategorySeriesData();
        disapprovalSeriesData.setName("不赞成2");
        List<Double> disapprovalDataList = new ArrayList<>();
        disapprovalSeriesData.setDataList(disapprovalDataList);
        chartSeriesDataList.add(disapprovalSeriesData);
        for (PptTableRowData rowData : rowDataList) {
            String dimensionName = (String) rowData.getCellDataList().get(0).getValue();
            categoryDataList.add(dimensionName);
            Integer approval = random.nextInt(90);
            Integer neutral = random.nextInt(100-approval);
            Integer disapproval = 100-approval-neutral;
            approvalDataList.add(Double.valueOf(approval));
            neutralDataList.add(Double.valueOf(neutral));
            disapprovalDataList.add(Double.valueOf(disapproval));
        }
//        Collections.reverse(categoryDataList);

        IChart chart = prismaPptUtil.getChart("eei-new-chart");
        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("子维度名称", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("赞成百分比", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("中立百分比", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("不赞成百分比", DataTypes.DATATABLE_DOUBLE));
        for (PptTableRowData rowData : rowDataList) {
            String dimensionName = (String) rowData.getCellDataList().get(0).getValue();
            Integer approval = random.nextInt(90);
            Integer neutral = random.nextInt(100-approval);
            Integer disapproval = 100-approval-neutral;
            DataRow row = dataTable.newRow();
            row.setString("子维度名称", dimensionName);
            row.setDouble("赞成百分比", approval);
            row.setDouble("中立百分比", neutral);
            row.setDouble("不赞成百分比", disapproval);
            dataTable.getRows().add(row);
        }
        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A16"));
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "D1"));
        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B16"));
        chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getFill().getSolidColor().setKnownColor(KnownColors.BLUE);

        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(0).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
            dataLabel.setPosition(ChartDataLabelPosition.CENTER);
//            dataLabel.setPercentageVisible(true);
        }

        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C11"));
        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(1).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
            dataLabel.setPosition(ChartDataLabelPosition.CENTER);
        }
        chart.getSeries().get(2).setValues(chart.getChartData().get("D2", "D11"));
        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(1).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
            dataLabel.setPosition(ChartDataLabelPosition.INSIDE_END);
        }
        TableRow firstRow = table.getTableRows().get(0);
        for (int i = 0; i < firstRow.getCount(); i ++) {
//            firstRow.get(i).get
//            firstRow.get(i).getTextFrame().setWordWrap(false);
//            double x = firstRow.get(1).getOffsetX();
//            double y = firstRow.get(1).getOffsetY();
//            System.out.println(firstRow.get(i).getOffsetX());
//            System.out.println(firstRow.get(i).getOffsetY());
        }
        double x = firstRow.get(1).getOffsetX();
        double y = firstRow.get(1).getOffsetY();
        System.out.println(x);
        System.out.println(y);
        System.out.println(firstRow.get(1).getHeight());
//        firstRow.get(0).getTextFrame().setWordWrap(false);
        float height = table.getHeight() - new BigDecimal(firstRow.getHeight()).floatValue();
        chart.setHeight(height);
        chart.getPlotArea().setHeight(height);
        TableRow secondRow = table.getTableRows().get(1);
        System.out.println(secondRow.get(1).getOffsetY());
        System.out.println("firstRow Height=: " + firstRow.getHeight());
//        chart.setTop(137.76378);
        System.out.println(table.getLeft());
        System.out.println(table.getTop());
        System.out.println(firstRow.getHeight());
        System.out.println(firstRow.get(0).getWidth());
//        System.out.println(firstRow.get(0).getHeight());
        Presentation ppt = prismaPptUtil.getPpt();
        chart.setTop(table.getTop()+ firstRow.getHeight());


        IShape shape = prismaPptUtil.getShape("eei-new-pic");
        System.out.println((table.getTop() + (firstRow.getHeight() - shape.getHeight()) / 2));
        shape.setTop((table.getTop() + (firstRow.getHeight() - shape.getHeight()) / 2));
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-赞成度表30.pptx");
    }

    @Test
    public void convertUnit() {
        //转换磅值到厘米
        PdfUnitConvertor convertor = new PdfUnitConvertor();
        float x = convertor.convertUnits((float) 400, PdfGraphicsUnit.Point, PdfGraphicsUnit.Centimeter);
        float y = convertor.convertUnits((float) 100, PdfGraphicsUnit.Point, PdfGraphicsUnit.Centimeter);
        System.out.printf("x：%f " + "y:%f", x, y);
        System.out.println();
        float x2 = convertor.convertUnits((float) 14.1111, PdfGraphicsUnit.Centimeter, PdfGraphicsUnit.Point);
        float y2 = convertor.convertUnits((float) 3.527778, PdfGraphicsUnit.Centimeter, PdfGraphicsUnit.Point);
        System.out.printf("x2：%f " + "y2:%f", x2, y2);
        System.out.println();
        System.out.println(convertor.convertUnits((float) 4.86, PdfGraphicsUnit.Centimeter, PdfGraphicsUnit.Point));
        System.out.println(convertor.convertUnits((float) 21.71, PdfGraphicsUnit.Centimeter, PdfGraphicsUnit.Point));
    }
}