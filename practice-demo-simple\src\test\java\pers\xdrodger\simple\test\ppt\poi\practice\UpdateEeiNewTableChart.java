package pers.xdrodger.simple.test.ppt.poi.practice;

import org.apache.poi.xslf.usermodel.XSLFChart;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTable;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.*;

import java.math.BigDecimal;
import java.util.*;


public class UpdateEeiNewTableChart {

    private Double getRandomDouble() {
        Random random = new Random();
        return new BigDecimal(random.nextDouble() * 100).setScale(1, BigDecimal.ROUND_UP).doubleValue();
    }


    @Test
    public void updateEeiLineText() throws Exception {
        PrismaPptUtil prismaPptUtil = new PrismaPptUtil(FileUtil.getInputFilePath() + "demo-赞成度表.pptx");
        XSLFSlide slide = prismaPptUtil.getSlideByShapeName("eei-new-table");
        XSLFTable table = prismaPptUtil.getTable(slide,"eei-new-table");
        XSLFChart chart = prismaPptUtil.getChart(slide,"eei-new-chart");
        List<String> headList = new ArrayList<>();
        headList.add(null);
        headList.add(null);
        headList.add("分析主体2");
        headList.add("VS公司整体2");
        headList.add("VS20202");
        headList.add("VS20212");
        headList.add("VS2023");
        headList.add("VS2024");
        headList.add("VS2025");
        headList.add("VS2026");

        List<PptTableRowData> rowDataList = new LinkedList<>();
        Random random = new Random();
        random.nextDouble();
        for (int  i = 0; i < 10; i ++) {
            PptTableRowData rowData = new PptTableRowData();
            List<PptTableCellData> cellDataList = new ArrayList<>();
            cellDataList.add(new PptTableCellData("子维度" + i));
            cellDataList.add(new PptTableCellData(""));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            rowData.setCellDataList(cellDataList);
            rowDataList.add(rowData);
        }
        PptTableData pptTableData = new PptTableData();
        pptTableData.setShapeName("eei-new-table");
        pptTableData.setHeadList(headList);
        pptTableData.setBodyList(rowDataList);

        prismaPptUtil.replaceTableContent(pptTableData);

        List<ChartCategorySeriesData> chartSeriesDataList = new ArrayList<>();
        ChartCategorySeriesData categoryData = new ChartCategorySeriesData();
        categoryData.setName("子维度名称");
        List<String> categoryDataList = new ArrayList<>();
        categoryData.setDataList(categoryDataList);
        chartSeriesDataList.add(categoryData);
        ChartCategorySeriesData approvalSeriesData = new ChartCategorySeriesData();
        approvalSeriesData.setName("赞成度2");
        List<Double> approvalDataList = new ArrayList<>();
        approvalSeriesData.setDataList(approvalDataList);
        chartSeriesDataList.add(approvalSeriesData);
        ChartCategorySeriesData neutralSeriesData = new ChartCategorySeriesData();
        neutralSeriesData.setName("中立2");
        List<Double> neutralDataList = new ArrayList<>();
        neutralSeriesData.setDataList(neutralDataList);
        chartSeriesDataList.add(neutralSeriesData);
        ChartCategorySeriesData disapprovalSeriesData = new ChartCategorySeriesData();
        disapprovalSeriesData.setName("不赞成2");
        List<Double> disapprovalDataList = new ArrayList<>();
        disapprovalSeriesData.setDataList(disapprovalDataList);
        chartSeriesDataList.add(disapprovalSeriesData);
        for (PptTableRowData rowData : rowDataList) {
           String dimensionName = (String) rowData.getCellDataList().get(0).getValue();
            categoryDataList.add(dimensionName);
            Integer approval = random.nextInt(90);
            Integer neutral = random.nextInt(100-approval);
            Integer disapproval = 100-approval-neutral;
            approvalDataList.add(Double.valueOf(approval));
            neutralDataList.add(Double.valueOf(neutral));
            disapprovalDataList.add(Double.valueOf(disapproval));
        }
        Collections.reverse(categoryDataList);
        PptChartData pptChartData = new PptChartData();
        pptChartData.setCategorySeriesDataList(chartSeriesDataList);
        pptChartData.setShapeName("eei-new-chart");
        prismaPptUtil.replaceChartContent(slide, pptChartData);

        prismaPptUtil.savePpt("demo-赞成度表.pptx");

    }
}
