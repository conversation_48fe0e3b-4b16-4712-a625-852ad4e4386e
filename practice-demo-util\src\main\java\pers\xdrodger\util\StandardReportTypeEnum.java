package pers.xdrodger.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> yan
 * @version 1.0
 * @date 2020/6/22 11:23
 */

@ToString(callSuper = true)
@FieldNameConstants
@Getter
@AllArgsConstructor
public enum StandardReportTypeEnum {

    SJTP("SJTP", "norm", "SJTP"),
    PDP("PDP", "norm", "PDP"),
    AMA("工作成就动机评测（AMA）","norm","AMA"),
    AT("能力评测（AT）","norm","AT"),
    EPA("职业性格评测（EPA）","norm","EPA"),
    CSI("危机评测（CSI）","norm","CSI"),
    CA("胜任力（CA）","norm","CA"),
    PWVO("个人成就动机（PWVO）","norm","PWVO"),
    PCA("性格与职业测评（PCA）","norm","PCA"),
    PTA("个人报告（PTA）","norm","PTA"),
    _360_DEGREES ("测评反馈报告（360°）","norm","_360_DEGREES"),
    _270_DEGREES ("测评反馈报告（270°）","norm","_270_DEGREES"),
    _270_DEGREES_ZHONGHUAN ("测评反馈报告（270°）","norm","_270_DEGREES"),
    _360_CUSTOMIZE ("自定义报告（360°）","norm","_360_CUSTOMIZE"),
    _270_CUSTOMIZE ("自定义报告（270°）","norm","_270_CUSTOMIZE"),
    _270_CUSTOMIZE_ZHONGHUAN ("自定义报告（270°）","norm","_270_CUSTOMIZE"),
    _360_TRAIN_CUSTOMIZE("360°培养自定义","norm","_360_TRAIN_CUSTOMIZE"),
    _360_TRAIN("360°培养","norm","_360_TRAIN"),
    MCA ("“领航” 测评(MCA)","norm","MCA"),
    MCA_MID ("“领航” 中层测评(MCA)","norm","MCA"),
    MCA_HIGH ("“领航” 高层测评(MCA)","norm","MCA"),
    PRISMA ("敬业度调研","prisma","PRISMA"),
    INVESTIGATION_RESEARCH("调研","prisma","PRISMA"),
    INVESTIGATION_RESEARCH_CUSTOM("调研自定义","prisma","PRISMA"),
    TIP_NEW("人才画像评测2.0（TIP）","norm","TIP_NEW"),
    TIP_NEW_2("人才画像评测（TIP）","norm","TIP_NEW_2"),
    TIP("人才画像评测（TIP）","norm","TIP");

    private String name;

    private String projectShowType;

    private String group;

    StandardReportTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public boolean is360() {
        return this.name().contains("360");
    }

    public boolean is270() {
        return this.name().contains("270");
    }

    public boolean is270Zhonghuan() {
        return this.name().contains("270") && this.name().contains("ZHONGHUAN");
    }

    public boolean isCustomize() {
        return this.name().contains("CUSTOMIZE") || this.name().contains("CUSTOM");
    }

    public boolean isPrisma() {
        return this.name().contains("PRISMA");
    }

    public boolean isInvestigationResearch() {
        return Objects.equals(this, INVESTIGATION_RESEARCH);
    }

    public boolean isInvestigationResearchCustom() {
        return Objects.equals(this, INVESTIGATION_RESEARCH_CUSTOM);
    }

    public boolean isEmployee() {
        if (this != null) {
            return this.name().contains("PRISMA") || this.name().contains("INVESTIGATION_RESEARCH");
        }
        return false;
    }

    public boolean isSJTP() {
        return this.name().contains("SJTP");
    }

    public boolean isPDP() {
        return this.name().contains("PDP");
    }

    public static boolean is360Or270(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return is360(reportType) || is270(reportType);
        }
        return false;
    }


    public static boolean is360(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return reportType.name().contains("360");
        }
        return false;
    }

    public static boolean is270(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return reportType.name().contains("270");
        }
        return false;
    }

    public boolean is270or360() {
        return this.name().contains("360")||this.name().contains("270");
    }

    public static boolean isCustomize(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return reportType.name().contains("CUSTOMIZE") || reportType.name().contains("CUSTOM");
        }
        return false;
    }

    public static boolean is360Customize(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return is360(reportType) && isCustomize(reportType);
        }
        return false;
    }

    public static boolean is360Standard(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return is360(reportType) && !isCustomize(reportType);
        }
        return false;
    }

    public static boolean is270Standard(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return is270(reportType) && !isCustomize(reportType);
        }
        return false;
    }

    public static boolean isMca(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return reportType.name().contains("MCA");
        }
        return false;
    }

    public static boolean isPrisma(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return reportType.name().contains("PRISMA");
        }
        return false;
    }

    public static boolean isEmployee(StandardReportTypeEnum reportType){
        if (reportType != null) {
            return reportType.name().contains("PRISMA") || reportType.name().contains("INVESTIGATION_RESEARCH");
        }
        return false;
    }

    public static boolean isTip(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return reportType.name().contains("TIP");
        }
        return false;
    }

    public static boolean isStandalone(StandardReportTypeEnum reportType) {
        if (reportType != null) {
            return is360(reportType) || is270(reportType) || reportType.isEmployeeEngagementGroup() || isTip(reportType);
        }
        return false;
    }

    public static List<StandardReportTypeEnum> getGroupList(StandardReportTypeEnum reportType) {
        List<StandardReportTypeEnum> list = new ArrayList<>();
        for (StandardReportTypeEnum item : values()) {
            if (Objects.equals(item.group, reportType.group)) {
                list.add(item);
            }
        }
        return list;
    }

    public boolean isEmployeeEngagementGroup() {
        return this.group.contains("PRISMA");
    }

    public static void main(String[] args) {
        System.out.println(StandardReportTypeEnum.is360(StandardReportTypeEnum._360_CUSTOMIZE));
        System.out.println(StandardReportTypeEnum.is360(StandardReportTypeEnum.TIP));
        System.out.println(isCustomize(StandardReportTypeEnum._360_CUSTOMIZE));
        System.out.println(isCustomize(StandardReportTypeEnum.TIP));
    }
}
