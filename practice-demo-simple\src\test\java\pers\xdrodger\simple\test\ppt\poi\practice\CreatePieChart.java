package pers.xdrodger.simple.test.ppt.poi.practice;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.Units;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFChart;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.geom.Rectangle2D;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

public class CreatePieChart {

    @Test
    public void createPieChart() throws Exception {
        String[] series = new String[] {
                "人数",
        };
        List<ChartData> dataList = getDataList();

        // Category Axis Data
        List<String> deptList = dataList.stream().map(ChartData::getName).collect(Collectors.toList());
        String[] categories = deptList.toArray(new String[0]);
        // Values
        List<Double> numList = dataList.stream().map(ChartData::getValue).collect(Collectors.toList());
        Double[] values = numList.toArray(new Double[0]);
        String chartTitle = "";
        XMLSlideShow ppt = new XMLSlideShow();
        XSLFSlide slide = ppt.createSlide();
        XSLFChart chart = ppt.createChart();
        Rectangle2D rect2D = new java.awt.Rectangle(fromCM(1.5), fromCM(4), fromCM(22), fromCM(14));
        slide.addChart(chart, rect2D);

        int valuesColumn = 1;

        final int numOfPoints = categories.length;
        final String categoryDataRange = chart.formatRange(new CellRangeAddress(1, numOfPoints, COLUMN_LANGUAGES, COLUMN_LANGUAGES));
        final String valuesDataRange = chart.formatRange(new CellRangeAddress(1, numOfPoints, valuesColumn, valuesColumn));
        final XDDFDataSource<?> categoriesData = XDDFDataSourcesFactory.fromArray(categories, categoryDataRange, COLUMN_LANGUAGES);
        final XDDFNumericalDataSource<? extends Number> valuesData = XDDFDataSourcesFactory.fromArray(values, valuesDataRange, valuesColumn);
        valuesData.setFormatCode("General");


        // 横轴-类别
        // Use a category axis for the bottom axis.
        XDDFChartAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
        // 纵轴-系列
        XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.TOP);
//        leftAxis.setTitle(series[0]+","+series[1]);
        leftAxis.setVisible(false);


        XDDFPieChartData data = (XDDFPieChartData) chart.createData(ChartTypes.PIE, null, leftAxis);
        XDDFPieChartData.Series series1 = (XDDFPieChartData.Series) data.addSeries(categoriesData, valuesData);
        series1.setTitle(series[0], chart.setSheetTitle(series[valuesColumn - 1], valuesColumn));

        data.setVaryColors(true);
//        data.setHoleSize(42);
//        data.setFirstSliceAngle(90);
        chart.plot(data);

        XDDFChartLegend legend = chart.getOrAddLegend();
        legend.setPosition(LegendPosition.LEFT);
        legend.setOverlay(false);

        chart.setTitleText(chartTitle);
        chart.setTitleOverlay(false);
        chart.setAutoTitleDeleted(false);

        // save the result
        try (OutputStream out = new FileOutputStream(FileUtil.getOutputFilePath() + "create-pie-chart.pptx")) {
            ppt.write(out);
        }
    }

    private static int fromCM(double cm) {
        return (int) (Math.rint(cm * Units.EMU_PER_CENTIMETER));
    }

    private List<ChartData> getDataList() {
        List<ChartData> result = new ArrayList<>();
        result.add(new ChartData("管理团队", 7.0, 0.0));
        result.add(new ChartData("综合事务部", 8.0, 0.0));
        result.add(new ChartData("工程部", 75.0, 0.0));
        result.add(new ChartData("生产部", 423.0, 0.0));
        return result;
    }

    @AllArgsConstructor
    @Data
    private class ChartData {
        private String name;
        private Double value;
        private Double value2;

    }

    private static final int COLUMN_LANGUAGES = 0;
    private static final int COLUMN_COUNTRIES = 1;
    private static final int COLUMN_SPEAKERS = 2;

    private List<List<Object>> getRowDataList() {
        List<List<Object>> rowDataList = new LinkedList<>();
        rowDataList.add(addParams("", "人数"));
        rowDataList.add(addParams("管理团队", 7));
        rowDataList.add(addParams("综合事务部", 8));
        rowDataList.add(addParams("工程部", 75));
        rowDataList.add(addParams("工程部", 423));
        return rowDataList;
    }

    private List<Object> addParams(Object... params) {
        List<Object> result = new LinkedList<>();
        for (int i =0; i < params.length; i ++) {
            result.add(params[i]);
        }
        return result;
    }

    /**
     * 一个系列的数据
     */
    public static class SeriesData {

        /**
         * value 系列的名字
         */
        public String name;

        public List<NameDouble> value;

        public SeriesData(java.util.List<NameDouble> value) {
            this.value = value;
        }

        public SeriesData(String name, List<NameDouble> value) {
            this.name = name;
            this.value = value;
        }

        public SeriesData() {
        }
    }

    public class NameDouble {

        public String name;

        /**
         */
        public double value;

        public NameDouble(String name, double value) {
            this.name = name;
            this.value = value;
        }

        public NameDouble() {
        }

    }
}
