package pers.xdrodger.simple.test.ppt.poi.practice;

import org.apache.poi.xslf.usermodel.*;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class CopySlideTest {

    private XMLSlideShow ppt;
    private String dirPath = "D:\\workspace\\practice-demo\\file\\input\\";
    private String fileName = "copy-slide-demo.pptx";
    private String outFileName = "copy-slide-demo-out.pptx";

    @Before
    public void setUp() throws Exception {
        String filePath = dirPath + fileName;
        ppt = new XMLSlideShow(new FileInputStream(filePath));
    }

    @Test
    public void testCopySlide() {
        String shapeName = "title";
        XSLFSlide defaultSlide = getSlideByShapeName(shapeName);
        int slideIndex = defaultSlide.getSlideNumber() - 1;
        List<Integer> slideIndexList = new ArrayList<>();
        for (int i =0; i < 3; i ++) {
            if (i == 0) {
                // pass
            } else {
                XSLFSlide newSlide = copySlide(slideIndex);
                slideIndex = newSlide.getSlideNumber() - 1;
            }
            slideIndexList.add(slideIndex);
        }
        for (Integer index : slideIndexList) {
            XSLFSlide slide = ppt.getSlides().get(index);
            replaceText(slide, shapeName, "this is " + slide.getSlideNumber() + " slide.");
        }
    }

    @After
    public void savePpt() throws Exception {
        String filePath = dirPath + outFileName;
        FileOutputStream out = new FileOutputStream(filePath);
        ppt.write(out);
        out.close();
        ppt.close();
    }

    private void replaceText(XSLFSlide slide, String shapeName, String value) {
        XSLFShape shape = getShape(slide, shapeName);
        if (shape == null) {
            return;
        }
        XSLFTextShape textShape = (XSLFTextShape) shape;
        List<XSLFTextParagraph> textParagraphs = textShape.getTextParagraphs();
        for (XSLFTextParagraph textParagraph : textParagraphs) {
            List<XSLFTextRun> textRuns = textParagraph.getTextRuns();
            for (XSLFTextRun textRun : textRuns) {
                textRun.setText(value);
            }
        }
    }

    private XSLFSlide copySlide(int index) {
        XSLFSlideLayout defaultSlideLayout = null;
        List<XSLFSlideMaster> slideMasters = ppt.getSlideMasters();
        for (XSLFSlideMaster slideMaster : slideMasters) {
            for (XSLFSlideLayout slideLayout : slideMaster.getSlideLayouts()) {
                if (Objects.equals(SlideLayout.TITLE_AND_CONTENT, slideLayout.getType())) {
                    defaultSlideLayout = slideLayout;
                    break;
                }
            }
        }
        XSLFSlide slide = ppt.getSlides().get(index);
        XSLFSlide newSlide = ppt.createSlide(defaultSlideLayout).importContent(slide);
        ppt.setSlideOrder(newSlide, slide.getSlideNumber());
        return newSlide;
    }

    private XSLFSlide getSlideByShapeName(String shapeName) {
        List<XSLFSlide> slides = ppt.getSlides();
        for (XSLFSlide slide : slides) {
            List<XSLFShape> shapes = slide.getShapes();
            for (XSLFShape shape : shapes) {
                if (shape.getShapeName().equals(shapeName)) {
                    return slide;
                }
            }
        }
        throw new InvalidParameterException("shape not exist");
    }

    public XSLFShape getShape(XSLFSlide slide, String shapeName) {
        List<XSLFShape> shapes = slide.getShapes();
        for (XSLFShape shape : shapes) {
            if (shape.getShapeName().equals(shapeName)) {
                return shape;
            }
        }
        throw new InvalidParameterException("shape not exist in slide");
    }
}
