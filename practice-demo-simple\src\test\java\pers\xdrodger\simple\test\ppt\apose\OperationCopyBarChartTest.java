package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.ISlide;
import com.spire.presentation.Presentation;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.util.Iterator;

public class OperationCopyBarChartTest {


    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-复制图表.pptx");
        String shapeName = "p2-custom-index-demographic-chart";
        IChart chart = prismaPptUtil.getChart(shapeName);
        Presentation presentation = prismaPptUtil.getPpt();

        ISlide slide1 = presentation.getSlides().get(0);
        System.out.println(chart.getLeft());
        System.out.println(chart.getWidth());
//        Rectangle2D.Double rect1 = new Rectangle2D.Double(chart.getLeft() + chart.getWidth() + 50, chart.getTop(), chart.getWidth(), chart.getHeight());
        Rectangle2D.Double rect1 = new Rectangle2D.Double(chart.getFrame().getRectangle().getX() + chart.getFrame().getRectangle().getWidth() + 30, chart.getFrame().getRectangle().getY(), chart.getFrame().getRectangle().getWidth(), chart.getFrame().getRectangle().getHeight());
        IChart newChart = slide1.getShapes().createChart(chart, rect1, 0);
        newChart.setName(shapeName);

        Rectangle2D.Double rect2 = new Rectangle2D.Double(chart.getFrame().getRectangle().getX() + chart.getFrame().getRectangle().getWidth() * 2 + 60, chart.getFrame().getRectangle().getY(), chart.getFrame().getRectangle().getWidth(), chart.getFrame().getRectangle().getHeight());
        slide1.getShapes().createChart(chart, rect2, 0);
        System.out.println(slide1.getShapes().get(0).getName());
        System.out.println(slide1.getShapes().get(1).getName());
        System.out.println(slide1.getShapes().get(2).getName());


        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-复制图表.pptx");
    }

}
