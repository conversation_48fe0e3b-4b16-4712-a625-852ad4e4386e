package pers.xdrodger.simple.test.ppt.poi;

import org.apache.poi.xslf.usermodel.*;
import org.junit.Test;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.List;

public class ReaderPptTest {
    public static void main(String[] args) throws Exception {
        // slide name has been set via VBA ...
        FileInputStream fis = new FileInputStream("D:\\workspace\\practice-demo\\file\\input\\8 敬业度+3.0-报告样本.pptx");
        XMLSlideShow ppt = new XMLSlideShow(fis);
        fis.close();
        XSLFSlide sl = ppt.getSlides().get(0);
        System.out.println(sl.getXmlObject().getCSld().getName());
        // set slide name via POI and validate it
        sl.getXmlObject().getCSld().setName("new name");
        FileOutputStream fos = new FileOutputStream("D:\\workspace\\practice-demo\\file\\output\\8 敬业度+3.0-报告样本2.pptx");
        ppt.write(fos);
        fos.close();
        ppt.close();
        fis = new FileInputStream("D:\\workspace\\practice-demo\\file\\output\\8 敬业度+3.0-报告样本2.pptx");
        ppt = new XMLSlideShow(fis);
        fis.close();
        System.out.println(sl.getXmlObject().getCSld().getName());
        ppt.close();

    }

    @Test
    public void testReaderTable() {
        try {
            FileInputStream fis = new FileInputStream("D:\\workspace\\practice-demo\\file\\input\\8 敬业度+3.0-报告样本.pptx");
//            FileInputStream fis = new FileInputStream("/Library/workspace/my-project/practice-demo/file/input/8 敬业度+3.0-报告样本.pptx");
            XMLSlideShow ppt = new XMLSlideShow(fis);
            fis.close();
            XSLFSlide slide = ppt.getSlides().get(11);
            XSLFTable t = null;
            List<XSLFShape> shapes = slide.getShapes();
            for (XSLFShape shape : shapes) {
                shape.getAnchor();
                if ("dyssqk-taable".equals(shape.getShapeName())) {
                    System.out.println("shapeId=" + shape.getShapeId());
                    System.out.println("shapeName=" + shape.getShapeName());
                    t = (XSLFTable) shape;
                    List<XSLFTableRow> rows = t.getRows();
                    for (XSLFTableRow row : rows ) {
                        List<XSLFTableCell> cells = row.getCells();
                        for (XSLFTableCell cell : cells) {
                            System.out.println(cell.getText());
                        }
                    }
                }
//                if (shape instanceof XSLFTable) {
//                    System.out.println("shapeId=" + shape.getShapeId());
//                    System.out.println("shapeName=" + shape.getShapeName());
//                    t = (XSLFTable) shape;
//                    List<XSLFTableRow> rows = t.getRows();
//                    for (XSLFTableRow row : rows ) {
//                        List<XSLFTableCell> cells = row.getCells();
//                        for (XSLFTableCell cell : cells) {
//                            System.out.println(cell.getText());
//                        }
//                    }
//                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
