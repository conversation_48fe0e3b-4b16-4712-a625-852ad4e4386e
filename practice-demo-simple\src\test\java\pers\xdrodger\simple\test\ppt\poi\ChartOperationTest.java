package pers.xdrodger.simple.test.ppt.poi;

import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xddf.usermodel.chart.XDDFChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xslf.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTChart;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPlotArea;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.LinkedList;
import java.util.List;

public class ChartOperationTest {

    private XMLSlideShow ppt;

    private String fileName = "ppt-demo.pptx";


    private boolean isWindows() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("windows")) {
            return true;
        }
        return false;
    }

    private String getInputFilePath() {
        if (isWindows()) {
            return "D:\\workspace\\practice-demo\\file\\input\\";
        }
        return "/Library/workspace/my-project/practice-demo/file/input/";
    }

    private String getOutputFilePath() {
        if (isWindows()) {
            return "D:\\workspace\\practice-demo\\file\\output\\";
        }
        return "/Library/workspace/my-project/practice-demo/file/output/";
    }

    @Before
    public void setUp() throws Exception {
        FileInputStream fis = new FileInputStream(getInputFilePath() + fileName);
        ppt = new XMLSlideShow(fis);
    }

    @Test
    public void readChart() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(1);
        for (XSLFShape shape : slide.getShapes()) {
            if (shape instanceof XSLFTable) {
                System.out.println(shape.getShapeName());
            }
            if (shape instanceof XSLFGraphicFrame && !(shape instanceof XSLFTable)) {
                System.out.println(shape.getShapeName());
                XSLFGraphicFrame graphicFrame = (XSLFGraphicFrame) shape;
                if (!graphicFrame.hasChart()) {
                    continue;
                }
                XSLFChart chart = graphicFrame.getChart();
                CTChart ctChart = chart.getCTChart();
//                CTPlotArea plotArea = ctChart.getPlotArea();
//                plotArea.getCatAxList();
                for (XDDFChartData chartData : chart.getChartSeries()) {
                    System.out.println(chartData.getSeriesCount());
                    for (XDDFChartData.Series series : chartData.getSeries()) {
                        XDDFDataSource xddfDataSource = series.getCategoryData();
                        System.out.println(xddfDataSource.getDataRangeReference());
                        XDDFNumericalDataSource numericalDataSource = series.getValuesData();
                        System.out.println(numericalDataSource.getDataRangeReference());
                    }
                    System.out.println(JSONObject.toJSONString(chartData.getCategoryAxis()));
//                    List<XDDFValueAxis> valueAxes = chartData.getValueAxes();
//                    for (XDDFValueAxis valueAxis : valueAxes) {
//                        System.out.println(JSONObject.toJSONString(valueAxis));
//                    }
//                    chartData.getSeries(0).
                }
            }
        }

    }

    @Test
    public void addDataToChart() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(1);
        for (XSLFShape shape : slide.getShapes()) {
            if (shape instanceof XSLFGraphicFrame && !(shape instanceof XSLFTable)) {
                System.out.println(shape.getShapeName());
                XSLFGraphicFrame graphicFrame = (XSLFGraphicFrame) shape;
                XSLFChart chart = graphicFrame.getChart();
                XSSFWorkbook workbook = chart.getWorkbook();
                XSSFSheet sheet = workbook.getSheetAt(0);
                Row row = sheet.createRow(sheet.getLastRowNum() + 1);
                row.createCell(0).setCellValue("技术部2");
                row.createCell(1).setCellValue("100");
                CTPlotArea plot = chart.getCTChart().getPlotArea();
                workbook.write(chart.getPackagePart().getOutputStream());
//                CTPieChart pieChart = plot.getPieChartArray(0);
//                int i = 0;
//                for (CTPieSer ser : pieChart.getSerList()) {
////                    updateChartCatAndNum(seriesDatas.get(i), ser.getTx(), ser.getCat(), ser.getVal());
////                    ++i;
//                }
            }
        }
        ppt.write(new FileOutputStream(getOutputFilePath() + fileName));

    }

    @Test
    public void addPieChart() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(1);
//        chart.setTitleText("部门");
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("pieChart");
        addRowDataList(sheet, getRowDataList());
//        chart.setWorkbook(workbook);

        Drawing drawing = sheet.createDrawingPatriarch();
//        ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 0, 5, 10, 15);
//        XSLFChart chart = drawing.cre(anchor);
//        ChartLegend legend = chart.getOrCreateLegend();
//        legend.setPosition(LegendPosition.TOP_RIGHT);
//        LineChartData data = chart.getChartDataFactory().createLineChartData();
//        // Use a category axis for the bottom axis.
//        ChartAxis bottomAxis = chart.getChartAxisFactory().createCategoryAxis(AxisPosition.BOTTOM);
//        ValueAxis leftAxis = chart.getChartAxisFactory().createValueAxis(AxisPosition.LEFT);
//        leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
//        ChartDataSource<Number> xs = DataSources.fromNumericCellRange(sheet, new CellRangeAddress(0, 0, 0, NUM_OF_COLUMNS - 1));
//        ChartDataSource<Number> ys1 = DataSources.fromNumericCellRange(sheet, new CellRangeAddress(1, 1, 0, NUM_OF_COLUMNS - 1));
//        ChartDataSource<Number> ys2 = DataSources.fromNumericCellRange(sheet, new CellRangeAddress(2, 2, 0, NUM_OF_COLUMNS - 1));
//        data.addSeries(xs, ys1);
//        data.addSeries(xs, ys2);
//        chart.plot(data, bottomAxis, leftAxis);
//        slide.addChart(chart);
        ppt.write(new FileOutputStream(getOutputFilePath() + fileName));
    }

    private void addRowDataList(Sheet sheet, List<List<Object>> rowDataList) {
        for (int i =0; i < rowDataList.size(); i ++) {
            List<Object> rowData = rowDataList.get(i);
            Row row = sheet.createRow(i);
            for (int j =0; j < rowData.size(); j ++) {
                Object data = rowData.get(j);
                if (data instanceof Integer) {
                    row.createCell(j).setCellValue((Integer) data);
                } else if (data instanceof Double) {
                    row.createCell(j).setCellValue((Double) data);
                } else {
                    row.createCell(j).setCellValue(String.valueOf(data));
                }
            }

        }

    }

    private List<List<Object>> getRowDataList() {
        List<List<Object>> rowDataList = new LinkedList<>();
        rowDataList.add(addParams("", "人数"));
        rowDataList.add(addParams("管理团队", 7));
        rowDataList.add(addParams("综合事务部", 8));
        rowDataList.add(addParams("工程部", 75));
        rowDataList.add(addParams("工程部", 423));
        return rowDataList;
    }

    private List<Object> addParams(Object... params) {
        List<Object> result = new LinkedList<>();
        for (int i =0; i < params.length; i ++) {
            result.add(params[i]);
        }
        return result;
    }
}
