package pers.xdrodger.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * Excel文件迁移工具类
 * 用于将指定目录下以"EXCEL报表"开头的文件根据文件名中的年份（23或24）迁移到对应的目标目录
 * 
 * <AUTHOR>
 */
public class ExcelFileMigrationUtil {
    
    // 源目录路径
    private static final String SOURCE_DIR_1 = "D:\\腾讯生产待处理文件\\未处理\\第二批 中心级";
    private static final String SOURCE_DIR_2 = "D:\\腾讯生产待处理文件\\未处理\\第一批 部门级";
    
    // 目标目录路径
    private static final String TARGET_DIR_23 = "D:\\腾讯生产待处理文件\\未处理\\23";
    private static final String TARGET_DIR_24 = "D:\\腾讯生产待处理文件\\未处理\\24";
    
    // 文件名前缀
    private static final String FILE_PREFIX = "EXCEL报表";
    
    // 年份匹配模式
    private static final Pattern YEAR_23_PATTERN = Pattern.compile(".*23.*");
    private static final Pattern YEAR_24_PATTERN = Pattern.compile(".*24.*");
    
    /**
     * 执行文件迁移操作
     */
    public static void migrateExcelFiles() {
        System.out.println("开始执行Excel文件迁移操作...");
        
        // 确保目标目录存在
        createDirectoryIfNotExists(TARGET_DIR_23);
        createDirectoryIfNotExists(TARGET_DIR_24);
        
        // 处理第一个源目录
        System.out.println("处理目录: " + SOURCE_DIR_1);
        processDirectory(SOURCE_DIR_1);
        
        // 处理第二个源目录
        System.out.println("处理目录: " + SOURCE_DIR_2);
        processDirectory(SOURCE_DIR_2);
        
        System.out.println("Excel文件迁移操作完成！");
    }
    
    /**
     * 处理指定目录下的文件（递归搜索所有子目录）
     *
     * @param sourceDir 源目录路径
     */
    private static void processDirectory(String sourceDir) {
        Path sourcePath = Paths.get(sourceDir);

        if (!Files.exists(sourcePath) || !Files.isDirectory(sourcePath)) {
            System.err.println("目录不存在或不是有效目录: " + sourceDir);
            return;
        }

        System.out.println("开始递归搜索目录: " + sourceDir);
        int processedCount = 0;

        try (Stream<Path> pathStream = Files.walk(sourcePath)) {
            List<Path> excelFiles = pathStream
                .filter(Files::isRegularFile)
                .filter(path -> path.getFileName().toString().startsWith(FILE_PREFIX))
                .toList();

            System.out.println("找到 " + excelFiles.size() + " 个以'" + FILE_PREFIX + "'开头的文件");

            for (Path filePath : excelFiles) {
                System.out.println("处理文件: " + filePath.toString());
                if (migrateFile(filePath.toFile())) {
                    processedCount++;
                }
            }

        } catch (IOException e) {
            System.err.println("遍历目录时发生错误: " + sourceDir + ", 错误: " + e.getMessage());
        }

        System.out.println("从目录 " + sourceDir + " 及其子目录处理了 " + processedCount + " 个文件");
    }
    
    /**
     * 迁移单个文件
     * 
     * @param file 要迁移的文件
     * @return 是否成功迁移
     */
    private static boolean migrateFile(File file) {
        String fileName = file.getName();
        String targetDir = determineTargetDirectory(fileName);
        
        if (targetDir == null) {
            System.out.println("跳过文件（文件名不包含23或24）: " + fileName);
            return false;
        }
        
        try {
            Path sourcePath = file.toPath();
            String uniqueFileName = generateUniqueFileName(targetDir, fileName);
            Path targetPath = Paths.get(targetDir, uniqueFileName);
            
            Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("成功迁移文件: " + fileName + " -> " + targetPath.toString());
            return true;
            
        } catch (IOException e) {
            System.err.println("迁移文件失败: " + fileName + ", 错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 根据文件名确定目标目录
     * 
     * @param fileName 文件名
     * @return 目标目录路径，如果无法确定则返回null
     */
    private static String determineTargetDirectory(String fileName) {
        if (YEAR_23_PATTERN.matcher(fileName).matches()) {
            return TARGET_DIR_23;
        } else if (YEAR_24_PATTERN.matcher(fileName).matches()) {
            return TARGET_DIR_24;
        }
        return null;
    }
    
    /**
     * 生成唯一的文件名（处理重名情况）
     * 
     * @param targetDir 目标目录
     * @param originalFileName 原始文件名
     * @return 唯一的文件名
     */
    private static String generateUniqueFileName(String targetDir, String originalFileName) {
        File targetFile = new File(targetDir, originalFileName);
        
        if (!targetFile.exists()) {
            return originalFileName;
        }
        
        // 分离文件名和扩展名
        String nameWithoutExt;
        String extension;
        int lastDotIndex = originalFileName.lastIndexOf('.');
        
        if (lastDotIndex > 0) {
            nameWithoutExt = originalFileName.substring(0, lastDotIndex);
            extension = originalFileName.substring(lastDotIndex);
        } else {
            nameWithoutExt = originalFileName;
            extension = "";
        }
        
        // 生成带序号的文件名
        int counter = 1;
        String newFileName;
        do {
            newFileName = nameWithoutExt + "(" + counter + ")" + extension;
            targetFile = new File(targetDir, newFileName);
            counter++;
        } while (targetFile.exists());
        
        return newFileName;
    }
    
    /**
     * 创建目录（如果不存在）
     * 
     * @param dirPath 目录路径
     */
    private static void createDirectoryIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            if (dir.mkdirs()) {
                System.out.println("创建目录: " + dirPath);
            } else {
                System.err.println("创建目录失败: " + dirPath);
            }
        }
    }
    
    /**
     * 获取指定目录下所有以"EXCEL报表"开头的文件列表（递归搜索所有子目录）
     *
     * @param dirPath 目录路径
     * @return 文件信息列表（包含相对路径）
     */
    public static List<FileInfo> getExcelReportFiles(String dirPath) {
        List<FileInfo> fileList = new ArrayList<>();
        Path sourcePath = Paths.get(dirPath);

        if (!Files.exists(sourcePath) || !Files.isDirectory(sourcePath)) {
            return fileList;
        }

        try (Stream<Path> pathStream = Files.walk(sourcePath)) {
            pathStream
                .filter(Files::isRegularFile)
                .filter(path -> path.getFileName().toString().startsWith(FILE_PREFIX))
                .forEach(path -> {
                    String relativePath = sourcePath.relativize(path).toString();
                    String fileName = path.getFileName().toString();
                    fileList.add(new FileInfo(fileName, relativePath, path.toString()));
                });

        } catch (IOException e) {
            System.err.println("遍历目录时发生错误: " + dirPath + ", 错误: " + e.getMessage());
        }

        return fileList;
    }

    /**
     * 文件信息类，用于存储文件的详细信息
     */
    public static class FileInfo {
        private final String fileName;
        private final String relativePath;
        private final String fullPath;

        public FileInfo(String fileName, String relativePath, String fullPath) {
            this.fileName = fileName;
            this.relativePath = relativePath;
            this.fullPath = fullPath;
        }

        public String getFileName() {
            return fileName;
        }

        public String getRelativePath() {
            return relativePath;
        }

        public String getFullPath() {
            return fullPath;
        }

        @Override
        public String toString() {
            return relativePath;
        }
    }
    
    /**
     * 预览迁移操作（不实际移动文件）
     */
    public static void previewMigration() {
        System.out.println("=== 迁移预览 ===");

        System.out.println("\n第二批 中心级目录下的EXCEL报表文件:");
        previewDirectoryFiles(SOURCE_DIR_1);

        System.out.println("\n第一批 部门级目录下的EXCEL报表文件:");
        previewDirectoryFiles(SOURCE_DIR_2);

        System.out.println("\n=== 目录结构预览 ===");
        System.out.println("第二批 中心级目录结构:");
        showDirectoryStructure(SOURCE_DIR_1, 3);

        System.out.println("\n第一批 部门级目录结构:");
        showDirectoryStructure(SOURCE_DIR_2, 3);
    }

    /**
     * 显示目录结构（限制显示深度）
     *
     * @param dirPath 目录路径
     * @param maxDepth 最大显示深度
     */
    public static void showDirectoryStructure(String dirPath, int maxDepth) {
        Path sourcePath = Paths.get(dirPath);

        if (!Files.exists(sourcePath) || !Files.isDirectory(sourcePath)) {
            System.out.println("  目录不存在: " + dirPath);
            return;
        }

        try (Stream<Path> pathStream = Files.walk(sourcePath, maxDepth)) {
            pathStream
                .filter(Files::isDirectory)
                .sorted()
                .forEach(path -> {
                    int depth = path.getNameCount() - sourcePath.getNameCount();
                    String indent = "  " + "  ".repeat(depth);
                    String dirName = path.getFileName().toString();
                    System.out.println(indent + "📁 " + dirName);
                });

        } catch (IOException e) {
            System.err.println("显示目录结构时发生错误: " + dirPath + ", 错误: " + e.getMessage());
        }
    }
    
    /**
     * 预览指定目录下的文件迁移情况（递归搜索所有子目录）
     *
     * @param sourceDir 源目录
     */
    private static void previewDirectoryFiles(String sourceDir) {
        List<FileInfo> files = getExcelReportFiles(sourceDir);

        if (files.isEmpty()) {
            System.out.println("  未找到以'EXCEL报表'开头的文件");
            return;
        }

        System.out.println("  找到 " + files.size() + " 个文件:");
        for (FileInfo fileInfo : files) {
            String fileName = fileInfo.getFileName();
            String relativePath = fileInfo.getRelativePath();
            String targetDir = determineTargetDirectory(fileName);

            if (targetDir != null) {
                System.out.println("    " + relativePath + " -> " + targetDir);
            } else {
                System.out.println("    " + relativePath + " -> [跳过：文件名不包含23或24]");
            }
        }
    }
    
    /**
     * 显示搜索统计信息
     */
    public static void showSearchStatistics() {
        System.out.println("=== 搜索统计信息 ===");

        // 统计第一个目录
        List<FileInfo> files1 = getExcelReportFiles(SOURCE_DIR_1);
        System.out.println("\n第二批 中心级目录:");
        System.out.println("  总文件数: " + files1.size());
        showFileStatistics(files1);

        // 统计第二个目录
        List<FileInfo> files2 = getExcelReportFiles(SOURCE_DIR_2);
        System.out.println("\n第一批 部门级目录:");
        System.out.println("  总文件数: " + files2.size());
        showFileStatistics(files2);

        // 总计
        int totalFiles = files1.size() + files2.size();
        System.out.println("\n总计: " + totalFiles + " 个EXCEL报表文件");
    }

    /**
     * 显示文件统计信息
     *
     * @param files 文件列表
     */
    private static void showFileStatistics(List<FileInfo> files) {
        long count23 = files.stream()
            .filter(f -> YEAR_23_PATTERN.matcher(f.getFileName()).matches())
            .count();

        long count24 = files.stream()
            .filter(f -> YEAR_24_PATTERN.matcher(f.getFileName()).matches())
            .count();

        long countOther = files.size() - count23 - count24;

        System.out.println("    包含'23'的文件: " + count23 + " 个");
        System.out.println("    包含'24'的文件: " + count24 + " 个");
        System.out.println("    其他文件: " + countOther + " 个");
    }

    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        // 显示搜索统计
        showSearchStatistics();

        System.out.println("\n" + "=".repeat(50));

        // 先预览迁移操作
        previewMigration();

        // 询问用户是否继续
        System.out.println("\n是否继续执行迁移操作？(请在代码中手动确认)");

        // 取消注释下面这行来执行实际的迁移操作
        // migrateExcelFiles();
    }
}
