package pers.xdrodger.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Excel文件迁移工具类
 * 用于将指定目录下以"EXCEL报表"开头的文件根据文件名中的年份（23或24）迁移到对应的目标目录
 * 
 * <AUTHOR>
 */
public class ExcelFileMigrationUtil {
    
    // 源目录路径
    private static final String SOURCE_DIR_1 = "D:\\腾讯生产待处理文件\\未处理\\第二批 中心级";
    private static final String SOURCE_DIR_2 = "D:\\腾讯生产待处理文件\\未处理\\第一批 部门级";
    
    // 目标目录路径
    private static final String TARGET_DIR_23 = "D:\\腾讯生产待处理文件\\未处理\\23";
    private static final String TARGET_DIR_24 = "D:\\腾讯生产待处理文件\\未处理\\24";
    
    // 文件名前缀
    private static final String FILE_PREFIX = "EXCEL报表";
    
    // 年份匹配模式
    private static final Pattern YEAR_23_PATTERN = Pattern.compile(".*23.*");
    private static final Pattern YEAR_24_PATTERN = Pattern.compile(".*24.*");
    
    /**
     * 执行文件迁移操作
     */
    public static void migrateExcelFiles() {
        System.out.println("开始执行Excel文件迁移操作...");
        
        // 确保目标目录存在
        createDirectoryIfNotExists(TARGET_DIR_23);
        createDirectoryIfNotExists(TARGET_DIR_24);
        
        // 处理第一个源目录
        System.out.println("处理目录: " + SOURCE_DIR_1);
        processDirectory(SOURCE_DIR_1);
        
        // 处理第二个源目录
        System.out.println("处理目录: " + SOURCE_DIR_2);
        processDirectory(SOURCE_DIR_2);
        
        System.out.println("Excel文件迁移操作完成！");
    }
    
    /**
     * 处理指定目录下的文件
     * 
     * @param sourceDir 源目录路径
     */
    private static void processDirectory(String sourceDir) {
        File dir = new File(sourceDir);
        
        if (!dir.exists() || !dir.isDirectory()) {
            System.err.println("目录不存在或不是有效目录: " + sourceDir);
            return;
        }
        
        File[] files = dir.listFiles();
        if (files == null) {
            System.err.println("无法读取目录内容: " + sourceDir);
            return;
        }
        
        int processedCount = 0;
        for (File file : files) {
            if (file.isFile() && file.getName().startsWith(FILE_PREFIX)) {
                if (migrateFile(file)) {
                    processedCount++;
                }
            }
        }
        
        System.out.println("从目录 " + sourceDir + " 处理了 " + processedCount + " 个文件");
    }
    
    /**
     * 迁移单个文件
     * 
     * @param file 要迁移的文件
     * @return 是否成功迁移
     */
    private static boolean migrateFile(File file) {
        String fileName = file.getName();
        String targetDir = determineTargetDirectory(fileName);
        
        if (targetDir == null) {
            System.out.println("跳过文件（文件名不包含23或24）: " + fileName);
            return false;
        }
        
        try {
            Path sourcePath = file.toPath();
            String uniqueFileName = generateUniqueFileName(targetDir, fileName);
            Path targetPath = Paths.get(targetDir, uniqueFileName);
            
            Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("成功迁移文件: " + fileName + " -> " + targetPath.toString());
            return true;
            
        } catch (IOException e) {
            System.err.println("迁移文件失败: " + fileName + ", 错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 根据文件名确定目标目录
     * 
     * @param fileName 文件名
     * @return 目标目录路径，如果无法确定则返回null
     */
    private static String determineTargetDirectory(String fileName) {
        if (YEAR_23_PATTERN.matcher(fileName).matches()) {
            return TARGET_DIR_23;
        } else if (YEAR_24_PATTERN.matcher(fileName).matches()) {
            return TARGET_DIR_24;
        }
        return null;
    }
    
    /**
     * 生成唯一的文件名（处理重名情况）
     * 
     * @param targetDir 目标目录
     * @param originalFileName 原始文件名
     * @return 唯一的文件名
     */
    private static String generateUniqueFileName(String targetDir, String originalFileName) {
        File targetFile = new File(targetDir, originalFileName);
        
        if (!targetFile.exists()) {
            return originalFileName;
        }
        
        // 分离文件名和扩展名
        String nameWithoutExt;
        String extension;
        int lastDotIndex = originalFileName.lastIndexOf('.');
        
        if (lastDotIndex > 0) {
            nameWithoutExt = originalFileName.substring(0, lastDotIndex);
            extension = originalFileName.substring(lastDotIndex);
        } else {
            nameWithoutExt = originalFileName;
            extension = "";
        }
        
        // 生成带序号的文件名
        int counter = 1;
        String newFileName;
        do {
            newFileName = nameWithoutExt + "(" + counter + ")" + extension;
            targetFile = new File(targetDir, newFileName);
            counter++;
        } while (targetFile.exists());
        
        return newFileName;
    }
    
    /**
     * 创建目录（如果不存在）
     * 
     * @param dirPath 目录路径
     */
    private static void createDirectoryIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            if (dir.mkdirs()) {
                System.out.println("创建目录: " + dirPath);
            } else {
                System.err.println("创建目录失败: " + dirPath);
            }
        }
    }
    
    /**
     * 获取指定目录下所有以"EXCEL报表"开头的文件列表
     * 
     * @param dirPath 目录路径
     * @return 文件列表
     */
    public static List<String> getExcelReportFiles(String dirPath) {
        List<String> fileList = new ArrayList<>();
        File dir = new File(dirPath);
        
        if (!dir.exists() || !dir.isDirectory()) {
            return fileList;
        }
        
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile() && file.getName().startsWith(FILE_PREFIX)) {
                    fileList.add(file.getName());
                }
            }
        }
        
        return fileList;
    }
    
    /**
     * 预览迁移操作（不实际移动文件）
     */
    public static void previewMigration() {
        System.out.println("=== 迁移预览 ===");
        
        System.out.println("\n第二批 中心级目录下的EXCEL报表文件:");
        previewDirectoryFiles(SOURCE_DIR_1);
        
        System.out.println("\n第一批 部门级目录下的EXCEL报表文件:");
        previewDirectoryFiles(SOURCE_DIR_2);
    }
    
    /**
     * 预览指定目录下的文件迁移情况
     * 
     * @param sourceDir 源目录
     */
    private static void previewDirectoryFiles(String sourceDir) {
        List<String> files = getExcelReportFiles(sourceDir);
        
        if (files.isEmpty()) {
            System.out.println("  未找到以'EXCEL报表'开头的文件");
            return;
        }
        
        for (String fileName : files) {
            String targetDir = determineTargetDirectory(fileName);
            if (targetDir != null) {
                System.out.println("  " + fileName + " -> " + targetDir);
            } else {
                System.out.println("  " + fileName + " -> [跳过：文件名不包含23或24]");
            }
        }
    }
    
    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        // 先预览迁移操作
        previewMigration();
        
        // 询问用户是否继续
        System.out.println("\n是否继续执行迁移操作？(请在代码中手动确认)");
        
        // 取消注释下面这行来执行实际的迁移操作
        // migrateExcelFiles();
    }
}
