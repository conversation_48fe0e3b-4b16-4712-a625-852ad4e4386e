package pers.xdrodger.simple.test.ppt.poi.practice;

import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFShape;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTable;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;

public class UpdateTable {

    public XSLFTable getTable(XMLSlideShow ppt, String shapeName) throws Exception {
        for (XSLFSlide slide : ppt.getSlides()) {
            for (XSLFShape shape : slide.getShapes()) {
                if (shape.getShapeName().equals(shapeName) && shape instanceof XSLFTable) {
                    return (XSLFTable) shape;
                }
            }
        }
        return null;
    }

    @Test
    public void updateTable() throws Exception {
        FileInputStream fis = new FileInputStream(FileUtil.getInputFilePath() + "ppt-demo.pptx");
        XMLSlideShow ppt = new XMLSlideShow(fis);
        XSLFTable table = getTable(ppt, "bmzcd-table");
        table.removeRow(4);
        table.removeColumn(3);
        // save the result
        try (OutputStream out = new FileOutputStream(FileUtil.getOutputFilePath() + "update-table.pptx")) {
            ppt.write(out);
        }
    }
}
