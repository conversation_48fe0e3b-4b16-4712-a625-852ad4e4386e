package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.KnownColors;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.TickLabelPositionType;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.util.Iterator;

public class OperationCultureRadarMapChartTest {

    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-文化雷达图.pptx");
        IChart chart = prismaPptUtil.getChart("p3-development-suggestions-motivation-radar-map-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
//            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        DataTable dataTable = new DataTable();

        dataTable.getColumns().add(new DataColumn("动机名称", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("组织思维", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("组织行为", DataTypes.DATATABLE_DOUBLE));

        DataRow row1 = dataTable.newRow();
        row1.setString("动机名称", "安全与稳定");
        row1.setDouble("组织思维", 4.49);
        row1.setDouble("组织行为", 4.64);
        DataRow row2 = dataTable.newRow();
        row2.setString("动机名称", "学习与成长");
        row2.setDouble("组织思维", 4.61);
        row2.setDouble("组织行为", 4.49);
        DataRow row3 = dataTable.newRow();
        row3.setString("动机名称", "成就与结果");
        row3.setDouble("组织思维", 4.64);
        row3.setDouble("组织行为", 4.61);
        DataRow row4 = dataTable.newRow();
        row4.setString("动机名称", "关系与归属");
        row4.setDouble("组织思维", 4.55);
        row4.setDouble("组织行为", 4.55);
        dataTable.getRows().add(row1);
        dataTable.getRows().add(row2);
        dataTable.getRows().add(row3);
        dataTable.getRows().add(row4);

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }

        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A5"));
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "C1"));
        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B5"));

        chart.getSeries().get(0).setType(ChartType.RADAR_FILLED);
        chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getFill().getSolidColor().setKnownColor(KnownColors.BLUE);

        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C5"));
        // 使用次坐标轴
        chart.getSeries().get(1).setUseSecondAxis(false);
        chart.getSeries().get(1).setType(ChartType.RADAR);
        chart.getSeries().get(1).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(1).getFill().getSolidColor().setKnownColor(KnownColors.GRAY);

        chart.getSecondaryValueAxis().getMajorGridTextLines().setFillType(FillFormatType.NONE);
        chart.getSecondaryValueAxis().setTickLabelPosition(TickLabelPositionType.TICK_LABEL_POSITION_NONE);
        chart.getPrimaryValueAxis().setTickLabelPosition(TickLabelPositionType.TICK_LABEL_POSITION_NONE);

        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-文化雷达图.pptx");
    }

}
