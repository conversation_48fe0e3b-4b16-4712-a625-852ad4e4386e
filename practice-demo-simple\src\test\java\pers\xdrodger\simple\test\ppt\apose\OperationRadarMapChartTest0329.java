package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.KnownColors;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class OperationRadarMapChartTest0329 {

    private List<List<Object>> getZhCNDataList() {
        List<List<Object>> result = new ArrayList<>();
        List<Object> item1 = new ArrayList<>();
        item1.add("好方向");
        item1.add(29.3);
        item1.add(31.7);
        item1.add(64.1);
        result.add(item1);
        List<Object> item2 = new ArrayList<>();
        item2.add("好工作");
        item2.add(32.1);
        item2.add(31.6);
        item2.add(70.8);
        result.add(item2);
        List<Object> item3 = new ArrayList<>();
        item3.add("好文化");
        item3.add(33.3);
        item3.add(31.8);
        item3.add(75.8);
        result.add(item3);
        List<Object> item4 = new ArrayList<>();
        item4.add("好员工");
        item4.add(31.8);
        item4.add(34.9);
        item4.add(62.7);
        result.add(item4);
        return result;
    }

    private List<List<Object>> getEnUsDataList() {
        List<List<Object>> result = new ArrayList<>();
        List<Object> item1 = new ArrayList<>();
        item1.add("Great  Orientation");
        item1.add(29.3);
        item1.add(31.7);
        item1.add(64.1);
        result.add(item1);
        List<Object> item2 = new ArrayList<>();
        item2.add("Great  Job");
        item2.add(32.1);
        item2.add(31.6);
        item2.add(70.8);
        result.add(item2);
        List<Object> item3 = new ArrayList<>();
        item3.add("Great  Culture");
        item3.add(33.3);
        item3.add(31.8);
        item3.add(75.8);
        result.add(item3);
        List<Object> item4 = new ArrayList<>();
        item4.add("Great  People");
        item4.add(31.8);
        item4.add(34.9);
        item4.add(62.7);
        result.add(item4);
        return result;
    }


    @Test
    public void testReplaceZhCN() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-雷达图-0329.pptx");
        IChart chart = prismaPptUtil.getChart("radar-map-chart");
        DataTable dataTable = new DataTable();
        String dimensionName = "维度名称";
        String dimensionScore = "维度得分";
        String company = "公司整体";
        String norm = "外部常模";
        dataTable.getColumns().add(new DataColumn(dimensionName, DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn(dimensionScore, DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn(company, DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn(norm, DataTypes.DATATABLE_DOUBLE));
        List<List<Object>> dataList = getZhCNDataList();
        for (int i = 0; i < dataList.size(); i ++) {
            List<Object> data = dataList.get(i);
            DataRow row = dataTable.newRow();
            for (int j = 0; j < data.size(); j ++) {
                if (j == 0) {
                    row.setString(dimensionName, (String) data.get(j));
                }
                if (j == 1) {
                    row.setDouble(dimensionScore, (Double) data.get(j));
                }
                if (j == 2) {
                    row.setDouble(company, (Double) data.get(j));
                }
                if (j == 3) {
                    row.setDouble(norm, (Double) data.get(j));
                }
            }
            dataTable.getRows().add(row);
        }

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A5"));
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "D1"));
        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B5"));
        chart.getSeries().get(0).setType(ChartType.RADAR_FILLED);
        chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getFill().getSolidColor().setKnownColor(KnownColors.BLUE);

        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C5"));
        // 使用次坐标轴
        chart.getSeries().get(1).setUseSecondAxis(true);
        chart.getSeries().get(1).setType(ChartType.RADAR);
        //将系列2绘制在次坐标轴
        chart.getSeries().get(2).setValues(chart.getChartData().get("D2", "D5"));
        chart.getSeries().get(2).setUseSecondAxis(true);
        chart.getSeries().get(2).setType(ChartType.RADAR);
        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-雷达图-0329-zhCN.pptx");
    }

    @Test
    public void testReplaceEnUS() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-雷达图-0329.pptx");
        IChart chart = prismaPptUtil.getChart("radar-map-chart");
        DataTable dataTable = new DataTable();
        String dimensionName = "Dimension";
        String dimensionScore = "Company";
        String company = "Company";
        String norm = "Norm";
        dataTable.getColumns().add(new DataColumn(dimensionName, DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn(dimensionScore, DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn(company, DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn(norm, DataTypes.DATATABLE_DOUBLE));
        List<List<Object>> dataList = getEnUsDataList();
        for (int i = 0; i < dataList.size(); i ++) {
            List<Object> data = dataList.get(i);
            DataRow row = dataTable.newRow();
            for (int j = 0; j < data.size(); j ++) {
                if (j == 0) {
                    row.setString(dimensionName, (String) data.get(j));
                }
                if (j == 1) {
                    row.setDouble(dimensionScore, (Double) data.get(j));
                }
                if (j == 2) {
                    row.setDouble(company, (Double) data.get(j));
                }
                if (j == 3) {
                    row.setDouble(norm, (Double) data.get(j));
                }
            }
            dataTable.getRows().add(row);
        }

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A5"));
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "D1"));
        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B5"));
        chart.getSeries().get(0).setType(ChartType.RADAR_FILLED);
        chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getFill().getSolidColor().setKnownColor(KnownColors.BLUE);

        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C5"));
        // 使用次坐标轴
        //chart.getSeries().get(1).setUseSecondAxis(true);
        chart.getSeries().get(1).setType(ChartType.RADAR);
        //将系列2绘制在次坐标轴
        chart.getSeries().get(2).setValues(chart.getChartData().get("D2", "D5"));
        //chart.getSeries().get(2).setUseSecondAxis(true);
        chart.getSeries().get(2).setType(ChartType.RADAR);
        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-雷达图-0329-enUS.pptx");
    }
}
