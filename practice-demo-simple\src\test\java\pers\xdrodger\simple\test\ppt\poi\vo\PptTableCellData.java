package pers.xdrodger.simple.test.ppt.poi.vo;

import com.spire.presentation.TextAlignmentType;
import com.spire.presentation.TextAnchorType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.awt.*;

@Builder
@AllArgsConstructor
@Data
public class PptTableCellData {
    private Object value;
    private boolean isNeedFillColor = false;
    // 字体大小
    private Float fontHeight;
    private Color fillColor;
    private Color fontColor;
    private Boolean isBold;
    private Boolean isItalic;
    private Color secondFontColor;

    /**
     * 单元格设置水平对齐方式
     */
    private TextAlignmentType textAlignmentType;

    /**
     * 单元格设置垂直对齐方式
     */
    private TextAnchorType textAnchorType;

    /**
     * 顶部边框
     */
    private ImmutablePair<Color, Double> topBorder;

    /**
     * 右边边框
     */
    private ImmutablePair<Color, Double> rightBorder;

    /**
     * 底部边框
     */
    private ImmutablePair<Color, Double> bottomBorder;

    /**
     * 左边边框
     */
    private ImmutablePair<Color, Double> leftBorder;

    public PptTableCellData() {

    }

    public PptTableCellData(Object value) {
        this.value = value;
    }
}
