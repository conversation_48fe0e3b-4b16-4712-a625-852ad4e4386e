package pers.xdrodger.simple.test.ppt.poi.practice;

import org.apache.poi.xslf.usermodel.*;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;


public class UpdateFourQuadrantChart {


    @Test
    public void updateEeiLineText() throws Exception {
        PrismaPptUtil prismaPptUtil = new PrismaPptUtil(FileUtil.getInputFilePath() + "demo-四象限图.pptx");
        XSLFSlide slide = prismaPptUtil.getSlideByShapeName("eei-line");
        System.out.println(slide.getSlideNumber());
        XSLFShape shape = prismaPptUtil.getShape(slide,"eei-line-text");
        XSLFTextShape textShape = (XSLFTextShape) shape;
        System.out.println(textShape.getText());
        for (XSLFTextParagraph textParagraph : textShape.getTextParagraphs()) {
            List<XSLFTextRun> textRuns = textParagraph.getTextRuns();
            for (XSLFTextRun textRun : textRuns) {
                System.out.println(textRun.getRawText());
            }
        }
        prismaPptUtil.replaceFirstTextAndRemoveOthers(slide, "eei-line-text", "敬业度指数（55.6）");
        prismaPptUtil.savePpt("demo-四象限图.pptx");


    }

    @Test
    public void updateEeiEsiText() throws Exception {
        PrismaPptUtil prismaPptUtil = new PrismaPptUtil(FileUtil.getInputFilePath() + "demo-四象限图.pptx");
        XSLFSlide slide = prismaPptUtil.getSlideByShapeName("eei-line");
        System.out.println(slide.getSlideNumber());
        XSLFShape shape = prismaPptUtil.getShape(slide,"h-eei-l-esi-text");
        XSLFTextShape textShape = (XSLFTextShape) shape;
        System.out.println(textShape.getText());
        for (XSLFTextParagraph textParagraph : textShape.getTextParagraphs()) {
            List<XSLFTextRun> textRuns = textParagraph.getTextRuns();
            for (XSLFTextRun textRun : textRuns) {
                System.out.println(textRun.getRawText());
            }
        }
        List<String> list = new ArrayList<>();
        for (int i =0 ; i < 15; i ++) {
            list.add("部门B1(敬业 48.4、满意 41.7)");
        }
        prismaPptUtil.replaceFirstTextAndRemoveOthers(slide, "h-eei-l-esi-text", String.join("\n", list));
        prismaPptUtil.savePpt("demo-四象限图.pptx");


    }
}
