package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

public class OperationAdjustTextTest {



    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil pptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-标题自适应.pptx");

        IShape shape = pptUtil.getShape("p2-overview-esi-department-n-name-text");
        IAutoShape autoShape = (IAutoShape) shape;
        autoShape.getTextFrame().setText("Department of Safety and Environment");
        autoShape.getTextFrame().setAutofitType(TextAutofitType.NORMAL);
//        autoShape.getTextFrame().setAutofitType(TextAutofitType.SHAPE);
//        autoShape.getTextFrame().setWordWrap(false);
        pptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-标题自适应.pptx");
    }

    @Test
    public void testReplace2() throws Exception {
        SpirePrismaPptUtil pptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-标题自适应2.pptx");

        IShape shape = pptUtil.getShape("p2-overview-esi-department-n-name-text");
        IAutoShape autoShape = (IAutoShape) shape;
        autoShape.getTextFrame().setText("Department of Safety and Environment");
//        autoShape.getTextFrame().setAutofitType(TextAutofitType.NORMAL);
        autoShape.getTextFrame().setAutofitType(TextAutofitType.SHAPE);
        autoShape.getTextFrame().setWordWrap(false);
        pptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-标题自适应2.pptx");
    }
}