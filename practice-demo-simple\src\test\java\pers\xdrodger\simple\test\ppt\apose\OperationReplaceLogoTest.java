package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import com.spire.presentation.drawing.*;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;


public class OperationReplaceLogoTest {


    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-替换logo.pptx");
        IAutoShape shape = (IAutoShape) prismaPptUtil.getShape("tenant-logo");
        ISlide slide = prismaPptUtil.getFirstSlide("tenant-logo");
//        FillFormat fillFormat = shape.getFill();
        shape.getFill().setFillType(FillFormatType.PICTURE);

//        Rectangle2D rect = new Rectangle2D.Double(shape.getLeft(), shape.getTop(), shape.getWidth(), shape.getHeight());
//        IEmbedImage image = slide.getShapes().appendEmbedImage(ShapeType.RECTANGLE, "lv-logo.png", rect);
//        IImageData iImageData = new sprfha();
//        BufferedImage bufferedImage = ImageIO.read( new FileInputStream( FileUtil.getInputFilePath() + "lv-logo.png" ));
        BufferedImage bufferedImage = ImageIO.read( new FileInputStream( FileUtil.getInputFilePath() + "adidas-logo.png" ));
        IImageData imageData = prismaPptUtil.getPpt().getImages().append(bufferedImage);
//        image.getLine().setFillType(FillFormatType.NONE);
        shape.getTextFrame().setText("");
        shape.getFill().getPictureFill().getPicture().setEmbedImage(imageData);
//        shape.getFill().getPictureFill().getPicture().setUrl("lv-logo.png");
        shape.getFill().getPictureFill().setFillType(PictureFillType.STRETCH);
        shape.getLine().setFillType(FillFormatType.NONE);
        shape.setUseBackgroundFill(false);
        System.out.println("");
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-替换logo.pptx");
    }
}