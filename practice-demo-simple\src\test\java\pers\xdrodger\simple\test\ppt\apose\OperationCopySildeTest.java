package pers.xdrodger.simple.test.ppt.apose;

import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

public class OperationCopySildeTest {

    @Test
    public void testCopy() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-散点图2.pptx");
        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-散点图2-复制.pptx");
    }
}
