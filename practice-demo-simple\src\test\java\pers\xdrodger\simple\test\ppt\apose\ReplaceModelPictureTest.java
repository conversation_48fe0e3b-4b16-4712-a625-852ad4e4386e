package pers.xdrodger.simple.test.ppt.apose;

import com.spire.pdf.graphics.PdfGraphicsUnit;
import com.spire.pdf.graphics.PdfUnitConvertor;
import com.spire.presentation.*;
import com.spire.presentation.drawing.IImageData;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;

public class ReplaceModelPictureTest {

    public static void main(String[] args) throws Exception {

        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-模型图.pptx");
        Presentation ppt = prismaPptUtil.getPpt();
        //添加图片到图片集合
        String imagePath = "D:\\workspace\\practice-demo\\file\\input\\22-22.png";
        BufferedImage bufferedImage = ImageIO.read(new FileInputStream(imagePath));
        IImageData image = ppt.getImages().append(bufferedImage);



        //转换像素到磅值
        PdfUnitConvertor convertor = new PdfUnitConvertor();
        float width = convertor.convertUnits(bufferedImage.getWidth(), PdfGraphicsUnit.Pixel, PdfGraphicsUnit.Point);
        float height = convertor.convertUnits(bufferedImage.getHeight(), PdfGraphicsUnit.Pixel, PdfGraphicsUnit.Point);
        SlidePicture modelImage = (SlidePicture) prismaPptUtil.getShape("p2-model-image");
        float modelHeight = modelImage.getHeight();
        float modelWidth = modelImage.getWidth();
        if (width > height) { // 宽图
            float rate = modelWidth / width;
            width = modelWidth;
            height = height * rate;
        } else { // 窄图
            float rate = modelHeight / height;
            height = modelHeight;
            width = width * rate;
        }
        //重置shape的宽高
//        modelImage.setWidth(width / 2);
//        modelImage.setHeight(height / 2);
        modelImage.setWidth(width);
        modelImage.setHeight(height);
        //插入图片
        modelImage.insertPicture(imagePath);
        modelImage.getPictureFill().getPicture().setEmbedImage(image);
        //保存文档
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-模型图.pptx");
    }
}