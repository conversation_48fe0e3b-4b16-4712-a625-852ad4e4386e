package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import com.spire.presentation.drawing.BackgroundType;
import com.spire.presentation.drawing.FillFormatType;
import com.spire.presentation.drawing.IImageData;
import com.spire.presentation.drawing.PictureFillType;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;


public class CreateSingleMasterSlide {
    public static void main(String[] args) throws Exception {
        System.out.println(System.getProperty("user.dir"));
        //创建PPT文档，并设置幻灯片大小
        Presentation ppt = new Presentation();
        ppt.getSlideSize().setType(SlideSizeType.SCREEN_16_X_9);


        //获取第一张母版
        IMasterSlide masterSlide = ppt.getMasters().get(0);

        //设置母版背景
        BufferedImage image = ImageIO.read(new FileInputStream("D:\\workspace\\practice-demo\\file\\input\\tp.jpeg"));
        IImageData imageData = ppt.getImages().append(image);
        masterSlide.getSlideBackground().setType(BackgroundType.CUSTOM);
        masterSlide.getSlideBackground().getFill().setFillType(FillFormatType.PICTURE);
        masterSlide.getSlideBackground().getFill().getPictureFill().setFillType(PictureFillType.STRETCH);
        masterSlide.getSlideBackground().getFill().getPictureFill().getPicture().setEmbedImage(imageData);

        //添加图片到母版
        image = ImageIO.read(new FileInputStream("D:\\workspace\\practice-demo\\file\\input\\logo.jpg"));
        imageData = ppt.getImages().append(image);
        IEmbedImage imageShape = masterSlide.getShapes().appendEmbedImage(ShapeType.RECTANGLE,imageData,new Rectangle2D.Float((float) ppt.getSlideSize().getSize().getWidth()-240,40,60,60));
        imageShape.getLine().setFillType(FillFormatType.NONE);

        //添加文字到母版
        IAutoShape textShape = masterSlide.getShapes().appendShape(ShapeType.RECTANGLE, new Rectangle2D.Float((float) ppt.getSlideSize().getSize().getWidth()-230,85,200,30));
        textShape.getTextFrame().setText("文娱传媒");
        textShape.getTextFrame().getTextRange().setFontHeight(20f);
        textShape.getTextFrame().getTextRange().getFill().setFillType(FillFormatType.SOLID);
        textShape.getTextFrame().getTextRange().getFill().getSolidColor().setColor(Color.black);
        textShape.getTextFrame().getTextRange().getParagraph().setAlignment(TextAlignmentType.CENTER);
        textShape.getFill().setFillType(FillFormatType.NONE);
        textShape.getLine().setFillType(FillFormatType.NONE);

        //添加一张幻灯片（创建PPT文档时，已默认生成一张幻灯片，这里添加一张幻灯片可对比查看母版添加效果）
        ppt.getSlides().append();

        //保存文档
        ppt.saveToFile("D:\\workspace\\practice-demo\\file\\output\\CreateSingleMasterSlide.pptx", FileFormat.PPTX_2013);
        ppt.dispose();
    }
}