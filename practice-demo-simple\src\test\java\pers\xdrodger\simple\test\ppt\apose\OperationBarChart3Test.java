package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.KnownColors;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartDataLabel;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.util.Iterator;

public class OperationBarChart3Test {

    @Test
    public void testRead() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-柱状图3.pptx");
        IChart chart = prismaPptUtil.getChart("bar-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
//            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        //DataTable dataTable = chart.getChartDataTable();
        Iterator<ChartSeriesDataFormat> iterator = chart.getSeries().iterator();
        while (iterator.hasNext()) {
            ChartSeriesDataFormat chartSerie = iterator.next();
            System.out.println(chartSerie.getNamedRange().get(0).getText());
            int valuesCount = chartSerie.getValues().getCount();
            for (int i =0; i < valuesCount; i ++) {
                CellRange cellRange = chartSerie.getValues().get(i);
                System.out.println(cellRange.getText());
//                cellRange.setNumberValue(Integer.valueOf((String) cellRange.getValue()) + 5);
            }
        }
        //保存文件
//        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-柱状图3.pptx");
    }

    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-柱状图3.pptx");
        IChart chart = prismaPptUtil.getChart("bar-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
//            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        DataTable dataTable = new DataTable();

        dataTable.getColumns().add(new DataColumn("维度名称", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("维度得分", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("公司整体", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("外部常模", DataTypes.DATATABLE_DOUBLE));

        DataRow row1 = dataTable.newRow();
        row1.setString("维度名称", "好领导2");
        row1.setDouble("维度得分", 5.3);
        row1.setDouble("公司整体", 3.4);
        row1.setDouble("外部常模", 3);

        dataTable.getRows().add(row1);

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "D1"));

        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A2"));

        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B2"));
        chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getFill().getSolidColor().setKnownColor(KnownColors.BLUE);


        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C2"));
//        chart.getSeries().get(1).getFill().getSolidColor().setKnownColor(KnownColors.LIGHT_GRAY);
//        chart.getSeries().get(1).setType(ChartType.LINE_MARKERS);
        //将系列2绘制在次坐标轴
//        chart.getSeries().get(1).setUseSecondAxis(true);
        chart.getSeries().get(2).setValues(chart.getChartData().get("D2", "D2"));
//        chart.getSeries().get(2).setType(ChartType.LINE_MARKERS);

        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(0).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
//            dataLabel.setPercentageVisible(true);
        }
        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(1).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
//            dataLabel.setPercentageVisible(true);
        }
        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(2).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
//            dataLabel.setPercentageVisible(true);
        }
        chart.getChartTitle().getTextProperties().setText("好领导2");
        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-柱状图3.pptx");
    }

}
