<?xml version="1.0" encoding="UTF-8" ?>
<testsuite tests="1" failures="0" name="pers.xdrodger.util.ExcelDataValidatorTest" time="0.004" errors="0" skipped="0">
  <properties>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk1.8.0_241\jre\bin"/>
    <property name="java.vm.version" value="25.241-b07"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="maven.multiModuleProjectDirectory" value="D:\file\knx\code\practice-demo\practice-demo-util"/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="path.separator" value=";"/>
    <property name="guice.disable.misplaced.annotation.check" value="true"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="user.script" value=""/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="user.dir" value="D:\file\knx\code\practice-demo\practice-demo-util"/>
    <property name="java.runtime.version" value="1.8.0_241-b07"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="java.endorsed.dirs" value="C:\Program Files\Java\jdk1.8.0_241\jre\lib\endorsed"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="line.separator" value="
"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="user.variant" value=""/>
    <property name="os.name" value="Windows 10"/>
    <property name="classworlds.conf" value="D:\software\apache-maven-3.6.3\bin\..\bin\m2.conf"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk1.8.0_241\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\\Program Files\\Java\\jdk1.8.0_241\\bin;C:\\Users\\<USER>\\.trae-cn\\tools\\maven\\latest\\bin;C:\\Users\\<USER>\\.trae-cn\\tools\\gradle\\latest\\bin;c:\\Users\\<USER>\\.trae-cn\\sdks\\workspaces\\e42f11e9\\versions\\node\\current;c:\\Users\\<USER>\\.trae-cn\\sdks\\versions\\node\\current;C:\Program Files\Java\jdk1.8.0_241\bin;C:\Users\<USER>\.trae-cn\tools\maven\latest\bin;C:\Users\<USER>\.trae-cn\tools\gradle\latest\bin;c:\Users\<USER>\.trae-cn\sdks\workspaces\e42f11e9\versions\node\current;c:\Users\<USER>\.trae-cn\sdks\versions\node\current;D:\software\Python\Python313\Scripts\;D:\software\Python\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_241\bin;C:\Program Files\Java\jdk1.8.0_241\jre\bin;D:\software\apache-maven-3.6.3\bin;D:\software\mysql-5.7.35-winx64\mysql-5.7.35-winx64\bin;C:\Program Files\dotnet\;D:\software\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\software\nodejs\;C:\Program Files\PowerShell\7\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Java\jdk1.8.0_241\bin;C:\Program Files\Java\jdk1.8.0_241\jre\bin;D:\software\apache-maven-3.6.3\bin;D:\software\mysql-5.7.35-winx64\mysql-5.7.35-winx64\bin;C:\Program Files\dotnet\;D:\software\nodejs\;D:\software\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\AppData\Local\Programs\Trae\bin;D:\software\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;."/>
    <property name="maven.conf" value="D:\software\apache-maven-3.6.3\bin\../conf"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.class.version" value="52.0"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="os.version" value="10.0"/>
    <property name="library.jansi.path" value="D:\software\apache-maven-3.6.3\bin\..\lib\jansi-native"/>
    <property name="user.home" value="C:\Users\<USER>\software\apache-maven-3.6.3\bin\..\boot\plexus-classworlds-2.6.0.jar"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.home" value="C:\Program Files\Java\jdk1.8.0_241\jre"/>
    <property name="sun.java.command" value="org.codehaus.plexus.classworlds.launcher.Launcher test"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="user.language" value="zh"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.version" value="1.8.0_241"/>
    <property name="java.ext.dirs" value="C:\Program Files\Java\jdk1.8.0_241\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.boot.class.path" value="C:\Program Files\Java\jdk1.8.0_241\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\rt.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\sunrsasign.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_241\jre\classes"/>
    <property name="sun.stderr.encoding" value="ms936"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="maven.home" value="D:\software\apache-maven-3.6.3\bin\.."/>
    <property name="file.separator" value="\"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="sun.stdout.encoding" value="ms936"/>
    <property name="sun.desktop" value="windows"/>
    <property name="sun.cpu.isalist" value="amd64"/>
  </properties>
  <testcase classname="pers.xdrodger.util.ExcelDataValidatorTest" name="testValidateData" time="0.004"/>
</testsuite>