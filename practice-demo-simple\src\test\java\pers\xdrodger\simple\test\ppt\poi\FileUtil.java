package pers.xdrodger.simple.test.ppt.poi;

public class FileUtil {
    public static boolean isWindows() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("windows")) {
            return true;
        }
        return false;
    }

    public static String getInputFilePath() {
        if (isWindows()) {
            return "D:\\file\\knx\\code\\practice-demo\\file\\input\\";
        }
        return "/Library/workspace/my-project/practice-demo/file/input/";
    }

    public static String getOutputFilePath() {
        if (isWindows()) {
            return "D:\\file\\knx\\code\\practice-demo\\file\\output2\\";
        }
        return "/Library/workspace/my-project/practice-demo/file/output/";
    }
}
