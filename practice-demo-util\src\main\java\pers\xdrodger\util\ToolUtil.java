package pers.xdrodger.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

public class ToolUtil {

    public static final String A2Z_STRING = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String DIGITAL_STRING = "0123456789";
    private static final String LOCALHOST = "127.0.0.1";
    private static final String LOCALHOST1 = "localhost";
    private static final String LOCALHOST2 = "0.0.0.0";
    private static final String LOCALHOST3 = "0:0:0:0:0:0:0:1";

    /**
     * list按大小分组
     */
    public static <T> List<List<T>> splitList(List<T> list, int groupSize) {
        if (list == null || list.size() == 0) {
            return Collections.EMPTY_LIST;
        }
        return Lists.partition(list, groupSize); // 使用guava进行分组
    }

    public static String generateSurveyCaptcha() {
        return RandomStringUtils.random(5, A2Z_STRING + DIGITAL_STRING);
    }


    public static String[] chars = new String[] { "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
            "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z" };


    public static String generateFiveShortUuid() {
        return generateShortUuid(5);
    }

    public static String generateShortUuid(Integer length) {
        if (length <= 0) {
            return null;
        }
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < length; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % chars.length]);
        }
        return shortBuffer.toString();

    }


    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }

    public static void main(String[] args) {
        HashSet<String> objects = new HashSet<>();
        for (int i = 0; i < 1000; i++) {
            objects.add(generateShortUuid(5));
        }
        System.out.println(objects.size());
    }

}
