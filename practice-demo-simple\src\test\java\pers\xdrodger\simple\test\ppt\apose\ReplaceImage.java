package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import com.spire.presentation.drawing.IImageData;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;

public class ReplaceImage {

    public static void main(String[] args) throws Exception {

        //创建Presentation对象
        Presentation presentation= new Presentation();

        //加载PowerPoint示例文档
        presentation.loadFromFile("D:\\workspace\\practice-demo\\file\\input\\ppt-demo-替换图片.pptx");

        //添加图片到图片集合
        String imagePath = "D:\\workspace\\practice-demo\\file\\input\\tp.jpeg";
        BufferedImage bufferedImage = ImageIO.read(new FileInputStream(imagePath));
        IImageData image = presentation.getImages().append(bufferedImage);

        //获取第一张幻灯片上形状集合
        ShapeCollection shapes = presentation.getSlides().get(0).getShapes();

        //遍历所有形状
        for (int i = 0; i < shapes.getCount(); i++) {
            //判断形状是否是图片
            if (shapes.get(i) instanceof SlidePicture) {

                //将图片形状用新图片填充
                ((SlidePicture) shapes.get(i)).getPictureFill().getPicture().setEmbedImage(image);
            }
        }

        //保存文档
        presentation.saveToFile("D:\\workspace\\practice-demo\\file\\output\\ppt-demo-替换图片-output.pptx", FileFormat.PPTX_2013);
    }
}