package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.KnownColors;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.ChartDataLabel;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

public class OperationGreatLeaderBarChartTest {


    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-好领导.pptx");
        IChart chart = prismaPptUtil.getChart("p2-overview-esi-parent-dimension-bar-chart");

        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("维度名称", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("维度得分", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("公司整体", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("外部常模", DataTypes.DATATABLE_DOUBLE));

        DataRow row1 = dataTable.newRow();
        row1.setString("维度名称", "好领导");
        row1.setDouble("维度得分", 50);
        row1.setDouble("公司整体", 41.3);
        row1.setDouble("外部常模", 85.3);

        dataTable.getRows().add(row1);

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "D1"));

        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A2"));

        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B2"));
        chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getFill().getSolidColor().setKnownColor(KnownColors.BLUE);


        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C2"));
        //将系列2绘制在次坐标轴
        chart.getSeries().get(2).setValues(chart.getChartData().get("D2", "D2"));

        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(0).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
        }
        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(1).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
        }
        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(2).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
        }
        chart.getChartTitle().getTextProperties().setText("好领导");

        chart.getPrimaryValueAxis().isVisible(true);
        chart.getPrimaryValueAxis().isAutoMax(false);
        chart.getPrimaryValueAxis().isAutoMin(false);
        chart.getPrimaryValueAxis().setMinValue(0);
        chart.getPrimaryValueAxis().setMaxValue(100);
        chart.getPrimaryValueAxis().setMajorUnit(10f);
        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-好领导.pptx");
    }

}
