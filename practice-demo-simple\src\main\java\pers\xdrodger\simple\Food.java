package pers.xdrodger.simple;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.lang3.RandomStringUtils;
import pers.xdrodger.simple.enums.FoodType;

import java.io.Serializable;
import java.util.UUID;

@Data
public class Food implements Serializable {
    private String id;
    private String name;
    private FoodType type;

    public static Food newAppleInstance() {
        Food food = new Food();
        food.setId(UUID.randomUUID().toString());
        food.setName("apple");
        food.setType(FoodType.Fruit);
        return food;
    }

    public static Food newFruitInstance() {
        Food food = new Food();
        food.setId(UUID.randomUUID().toString());
        food.setName(RandomStringUtils.randomAlphabetic(5));
        food.setType(FoodType.Fruit);
        return food;
    }

    public static void main(String[] args) {
        Food apple = Food.newAppleInstance();
        System.out.println(JSON.toJSONString(apple));
        
    }
}
