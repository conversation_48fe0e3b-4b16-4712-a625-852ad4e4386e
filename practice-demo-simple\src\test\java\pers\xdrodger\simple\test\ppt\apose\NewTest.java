package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.Presentation;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

/**
 * <AUTHOR>
 * @Title: NewTest
 * @ProjectName practice-demo
 * @Description:
 * @date 2021/11/21 11:46
 */
public class NewTest {
    @Test
    public void test() throws Exception {
        SpirePrismaPptUtil pptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "5G-敬业度模板.pptx");
        //Presentation presentation = new Presentation();
        //presentation.loadFromFile(FileUtil.getInputFilePath() + "5G-敬业度模板.pptx");
        System.out.println(pptUtil.getPpt().getSlides().getCount());
        System.out.println("--");
    }
}
