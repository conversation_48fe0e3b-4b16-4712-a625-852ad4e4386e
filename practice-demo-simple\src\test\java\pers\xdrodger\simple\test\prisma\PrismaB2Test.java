package pers.xdrodger.simple.test.prisma;

import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class PrismaB2Test {
    @Test
    public void test() {
        double[] xArray = new double[]{4.0, 6.0, 6.0, 1.0, 6.0, 2.0, 2.0, 4.0, 2.0, 2.0, 3.0, 6.0, 6.0, 4.0, 3.0, 4.0, 3.0, 1.0, 6.0, 6.0, 3.0, 6.0, 6.0, 6.0, 6.0, 2.0, 6.0, 2.0, 5.0, 1.0, 4.0, 1.0, 1.0, 2.0, 1.0, 2.0, 2.0};
        double[] yArray = new double[]{3.0, 1.0, 5.0, 6.0, 5.0, 3.0, 4.0, 4.0, 4.0, 1.0, 5.0, 4.0, 4.0, 2.0, 6.0, 6.0, 1.0, 3.0, 1.0, 1.0, 1.0, 4.0, 5.0, 3.0, 1.0, 4.0, 5.0, 2.0, 1.0, 5.0, 2.0, 1.0, 1.0, 5.0, 3.0, 4.0, 2.0};

        double[] xArray2 = new double[]{6.0, 2.0, 2.0, 6.0, 2.0, 6.0, 2.0, 2.0, 6.0, 4.0, 5.0, 4.0, 2.0, 6.0, 2.0, 2.0, 1.0, 1.0, 3.0, 3.0, 1.0, 6.0, 3.0, 4.0, 6.0, 6.0, 2.0, 4.0, 4.0, 6.0, 6.0, 1.0, 1.0, 6.0, 1.0, 3.0, 6.0};
        double[] yArray2 = new double[]{1.0, 5.0, 3.0, 3.0, 5.0, 4.0, 1.0, 5.0, 4.0, 2.0, 5.0, 4.0, 4.0, 2.0, 4.0, 1.0, 1.0, 4.0, 1.0, 3.0, 1.0, 1.0, 2.0, 4.0, 6.0, 2.0, 3.0, 6.0, 3.0, 5.0, 6.0, 5.0, 4.0, 1.0, 1.0, 1.0, 5.0};

        List<Double> xArrayList = new ArrayList<>();
        for (int i = 0; i < xArray.length; i ++) {
            xArrayList.add(xArray[i]);
        }
        List<Double> yArrayList = new ArrayList<>();
        for (int i = 0; i < yArray.length; i ++) {
            yArrayList.add(yArray[i]);
        }
        List<Double> xArray2List = new ArrayList<>();
        for (int i = 0; i < xArray2.length; i ++) {
            xArray2List.add(xArray2[i]);
        }
        List<Double> yArray2List = new ArrayList<>();
        for (int i = 0; i < yArray2.length; i ++) {
            yArray2List.add(yArray2[i]);
        }
        PearsonsCorrelation pearsonsCorrelation = new PearsonsCorrelation();
        double r = pearsonsCorrelation.correlation(xArray, yArray);
        System.out.println(r);
        double r2 = pearsonsCorrelation.correlation(xArray2, yArray2);
        System.out.println(r2);

        Collections.sort(xArrayList, new Comparator<Double>() {
            @Override
            public int compare(Double o1, Double o2) {
                return o1.compareTo(o2);
            }
        });
        Collections.sort(yArrayList, new Comparator<Double>() {
            @Override
            public int compare(Double o1, Double o2) {
                return o1.compareTo(o2);
            }
        });
        Collections.sort(xArray2List, new Comparator<Double>() {
            @Override
            public int compare(Double o1, Double o2) {
                return o1.compareTo(o2);
            }
        });
        Collections.sort(yArray2List, new Comparator<Double>() {
            @Override
            public int compare(Double o1, Double o2) {
                return o1.compareTo(o2);
            }
        });
        System.out.println(xArrayList.containsAll(xArray2List) + "-" + xArray2List.containsAll(xArrayList));
        System.out.println(yArrayList.containsAll(yArray2List) + "-" + yArray2List.containsAll(yArrayList));
        System.out.println(xArrayList.toString());
        System.out.println(xArray2List.toString());
        System.out.println(yArrayList.toString());
        System.out.println(yArray2List.toString());
    }
}
