package pers.xdrodger.simple.test.ppt.apose;

import com.spire.ms.System.Collections.IEnumerator;
import com.spire.presentation.*;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.ITrendlines;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartDataLabel;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.util.Iterator;

public class ReadScatterChart {

    private IChart getChart(ISlide slide, String tableName) {
        ShapeCollection shapeCollection = slide.getShapes();
        IEnumerator it = shapeCollection.iterator();
        while (it.hasNext()) {
            IShape shape = (IShape) it.next();
            //System.out.println(shape.getName());
            if (shape.getName().equals(tableName)) {
                return (IChart) shape;
            }
        }
        return null;
    }

    @Test
    public void testReadChart() throws Exception {
        //实例化一个Presentation对象
        Presentation presentation = new Presentation();

        presentation.loadFromFile(FileUtil.getInputFilePath() + "demo-散点图.pptx");
        ISlide slide = presentation.getSlides().get(0);
        IChart chart = getChart(slide, "depart-eei-oci-analysis-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
        }
        //DataTable dataTable = chart.getChartDataTable();
//        Iterator<ChartSeriesDataFormat> iterator = chart.getSeries().iterator();
//®        while (iterator.hasNext()) {
//            ChartSeriesDataFormat chartSerie = iterator.next();
//            System.out.println(chartSerie.getNamedRange().get(0).getText());
//            Iterator<CellRange> iterator1 = chartSerie.getValues().iterator();
//            while (iterator1.hasNext()) {
//                CellRange cellRange = iterator1.next();
//                System.out.println(cellRange.getText());
//                //cellRange.setNumberValue(5);
//            }
//            Iterator<ChartDataLabel> iterator2 = chartSerie.getDataLabels().iterator();
//            while (iterator2.hasNext()) {
//                ChartDataLabel chartDataLabel = iterator2.next();
//                System.out.println(chartDataLabel.getX());
//                System.out.println(chartDataLabel.getY());
//            }
//
//        }
        System.out.println(chart.getChartData().get(0, 0).getText());
        System.out.println(chart.getChartData().get(0, 1).getText());
        System.out.println(chart.getChartData().get(0, 2).getText());
        System.out.println(chart.getChartData().get(0, 3).getText());
        chart.getChartData().get(0, 0).setText("部门");
        chart.getChartData().get(0, 1).setText("x轴");
        chart.getChartData().get(0, 2).setText("Y 值2");
        chart.getChartData().get(0, 3).setText("切分线2");

        //Double[] xData = new Double[] { 0.8, 0.4, 0.5, 0.9 };
        String[] cData = new String[] {"射手座2", "调研2", "sag2", "prisma2"};
        Double[] xData = new Double[] { 0.8, 0.4, 0.5, 0.9 };
        Double[] yData = new Double[] { 0.3, 0.2, 0.7, 0.8 };
        for (int i =0; i < xData.length; i ++) {
            chart.getChartData().get(i+1,0).setValue(cData[i]);
            chart.getChartData().get(i+1,1).setValue(xData[i]);
            chart.getChartData().get(i+1,2).setValue(yData[i]);
        }
        chart.getChartData().get(1, 3).setValue(0.6);
        chart.getChartData().get(1, 4).setValue(0.7);
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("C1","D1"));

        //设置X和Y轴值
        chart.getSeries().get(1).setXValues(chart.getChartData().get("B2","B5"));
        chart.getSeries().get(1).setYValues(chart.getChartData().get("C2","C5"));
        // 设置切分线（误差线）
        chart.getSeries().get(0).setXValues(chart.getChartData().get("D2","D2"));
        chart.getSeries().get(0).setXValues(chart.getChartData().get("E2","E2"));
        chart.getSeries().get(0).getErrorBarsXFormat().setMinusVal(0);
        chart.getSeries().get(0).getErrorBarsXFormat().setErrorBarVal(1.0f);
        chart.getSeries().get(0).getErrorBarsXFormat().setPlusVal(0.2f);


        //保存文件
        presentation.saveToFile(FileUtil.getOutputFilePath() + "demo-散点图.pptx", FileFormat.PPTX_2013);
    }

    public static void main(String[] args) throws Exception {




    }

}