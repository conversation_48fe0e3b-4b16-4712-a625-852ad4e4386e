# 数据库设计文档

**数据库名：** survey_common

**文档版本：** 1.0.0

**文档描述：** 数据库设计文档生成

| 表名                  | 说明       |
| :---: | :---: |
| [flyway_schema_history](#flyway_schema_history) |  |
| [survey_standard_demographic](#survey_standard_demographic) | 标准人口统计学表 |
| [survey_standard_demographic_group](#survey_standard_demographic_group) | 标准人口统计学表 |
| [survey_standard_demographic_question](#survey_standard_demographic_question) | 标准人口统计学题目表 |
| [survey_standard_dimension](#survey_standard_dimension) | 标准维度表 |
| [survey_standard_dimension_bak](#survey_standard_dimension_bak) |  |
| [survey_standard_dimension_mapping](#survey_standard_dimension_mapping) | 标准维度父子级映射表 |
| [survey_standard_dimension_question_mapping](#survey_standard_dimension_question_mapping) | 标准维度题目映射表 |
| [survey_standard_dimension_tenant_mapping](#survey_standard_dimension_tenant_mapping) | 标准维度和公司的映射表 |
| [survey_standard_norm](#survey_standard_norm) | 标准常模表 |
| [survey_standard_norm_question_mapping](#survey_standard_norm_question_mapping) | 标准prisma常模问题mapping |
| [survey_standard_norm_tenant_mapping](#survey_standard_norm_tenant_mapping) | 标准常模表 |
| [survey_standard_norm_value](#survey_standard_norm_value) | 标准常模值表 |
| [survey_standard_option](#survey_standard_option) | 选项表 |
| [survey_standard_option_introduction](#survey_standard_option_introduction) | 选项填答说明和规则 |
| [survey_standard_package](#survey_standard_package) | 标准常模表 |
| [survey_standard_package_question_mapping](#survey_standard_package_question_mapping) | 题目包题目映射表 |
| [survey_standard_prisma_norm](#survey_standard_prisma_norm) | 标准prisma常模表 |
| [survey_standard_question](#survey_standard_question) | 标准题目表 |
| [survey_standard_questionnaire](#survey_standard_questionnaire) | 标准问卷表 |
| [survey_standard_questionnaire_category](#survey_standard_questionnaire_category) | 标准问卷分类表 |
| [survey_standard_questionnaire_dimension](#survey_standard_questionnaire_dimension) | 标准问卷维度表 |
| [survey_standard_questionnaire_dimension_mapping](#survey_standard_questionnaire_dimension_mapping) | 标准问卷维度映射表 |
| [survey_standard_questionnaire_dimension_question_mapping](#survey_standard_questionnaire_dimension_question_mapping) | 标准问卷维度题目映射表 |
| [survey_standard_questionnaire_question](#survey_standard_questionnaire_question) | 标准问卷分类表 |
| [survey_standard_questionnaire_relation_permission](#survey_standard_questionnaire_relation_permission) | 标准问卷关联权限表 |
| [survey_standard_questionnaire_scene_type](#survey_standard_questionnaire_scene_type) | 标准问卷场景类型表 |
| [survey_standard_questionnaire_scene_type_questionnaire_mapping](#survey_standard_questionnaire_scene_type_questionnaire_mapping) |  |
| [survey_standard_questionnaire_tenant_mapping](#survey_standard_questionnaire_tenant_mapping) | 标准问卷分配公司表 |
| [survey_standard_sag_norm_transform](#survey_standard_sag_norm_transform) | 标准报告分值表 |
| [survey_standard_sag_report_dimension](#survey_standard_sag_report_dimension) | 报告维度 |
| [survey_standard_sag_report_module](#survey_standard_sag_report_module) | 标准报告模块表 |
| [survey_standard_sag_report_score](#survey_standard_sag_report_score) | 标准报告分值表 |
| [survey_standard_sag_report_template](#survey_standard_sag_report_template) | 标准报告模板表 |
| [survey_standard_sag_report_template_config](#survey_standard_sag_report_template_config) | 标准报告模板配置表 |
| [survey_standard_sag_report_template_job](#survey_standard_sag_report_template_job) | 标准报告岗位表 |

**表名：** <a id="flyway_schema_history">flyway_schema_history</a>

**说明：** 

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | installed_rank |   int4   | 10 |   0    |    N     |  Y   |       |   |
|  2   | version |   varchar   | 50 |   0    |    Y     |  N   |       |   |
|  3   | description |   varchar   | 200 |   0    |    N     |  N   |       |   |
|  4   | type |   varchar   | 20 |   0    |    N     |  N   |       |   |
|  5   | script |   varchar   | 1000 |   0    |    N     |  N   |       |   |
|  6   | checksum |   int4   | 10 |   0    |    Y     |  N   |       |   |
|  7   | installed_by |   varchar   | 100 |   0    |    N     |  N   |       |   |
|  8   | installed_on |   timestamp   | 29 |   6    |    N     |  N   |   now()    |   |
|  9   | execution_time |   int4   | 10 |   0    |    N     |  N   |       |   |
|  10   | success |   bool   | 1 |   0    |    N     |  N   |       |   |

**表名：** <a id="survey_standard_demographic">survey_standard_demographic</a>

**说明：** 标准人口统计学表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | standard_demographic_group_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准人口统计学分组ID  |
|  3   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 标准人口统计学名称  |
|  4   | parent_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准人口统计学父级  |
|  5   | sort |   int4   | 10 |   0    |    Y     |  N   |       | 标准人口统计学排序  |
|  6   | basic_label |   varchar   | 100 |   0    |    Y     |  N   |       | 标准人口统计学基础标签  |
|  7   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  9   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  10   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  11   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  12   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  13   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |   'ASSESSMENT'::charactervarying    | 调研类型  |
|  14   | scope |   varchar   | 50 |   0    |    Y     |  N   |   'ALL'::charactervarying    | 适用对象全员普通员工高管  |

**表名：** <a id="survey_standard_demographic_group">survey_standard_demographic_group</a>

**说明：** 标准人口统计学表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 标准人口统计分组名称  |
|  3   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  4   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  5   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  7   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  8   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_demographic_question">survey_standard_demographic_question</a>

**说明：** 标准人口统计学题目表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 标准人口统计学题目名称  |
|  3   | standard_demographic_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准人口统计学ID  |
|  4   | is_require |   bool   | 1 |   0    |    Y     |  N   |       | 是否必填  |
|  5   | type |   varchar   | 100 |   0    |    Y     |  N   |       | 标准人口统计学类型  |
|  6   | rule |   varchar   | 100 |   0    |    Y     |  N   |       | 标准人口统计学规则  |
|  7   | sort |   int4   | 10 |   0    |    Y     |  N   |       | 排序  |
|  8   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  9   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  10   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  11   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  12   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  13   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  14   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |   'ASSESSMENT'::charactervarying    | 调研类型  |

**表名：** <a id="survey_standard_dimension">survey_standard_dimension</a>

**说明：** 标准维度表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度名称  |
|  3   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |       | 调研类型  |
|  4   | group_type |   varchar   | 50 |   0    |    Y     |  N   |       | 标准维度类型子维度大维度  |
|  5   | code |   varchar   | 50 |   0    |    Y     |  N   |       | 维度code  |
|  6   | standard_package_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准题目包ID  |
|  7   | algorithm_type |   varchar   | 50 |   0    |    Y     |  N   |       | 计算方式只有大维度才有  |
|  8   | description |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度描述  |
|  9   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  10   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  11   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  12   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  13   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  14   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  15   | scope |   varchar   | 50 |   0    |    Y     |  N   |   'ALL'::charactervarying    | 适用对象全员普通员工高管  |
|  16   | level |   varchar   | 50 |   0    |    Y     |  N   |   'NONE'::charactervarying    | 层级  |

**表名：** <a id="survey_standard_dimension_bak">survey_standard_dimension_bak</a>

**说明：** 

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       |   |
|  3   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |       |   |
|  4   | group_type |   varchar   | 50 |   0    |    Y     |  N   |       |   |
|  5   | code |   varchar   | 50 |   0    |    Y     |  N   |       |   |
|  6   | standard_package_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  7   | algorithm_type |   varchar   | 50 |   0    |    Y     |  N   |       |   |
|  8   | description |   json   | 2147483647 |   0    |    Y     |  N   |       |   |
|  9   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  10   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  11   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  12   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  13   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  14   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  15   | description_bak |   varchar   | 50 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_dimension_mapping">survey_standard_dimension_mapping</a>

**说明：** 标准维度父子级映射表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | parent_id |   varchar   | 36 |   0    |    N     |  N   |       | 父级ID  |
|  2   | child_id |   varchar   | 36 |   0    |    N     |  N   |       | 子级ID  |
|  3   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_dimension_question_mapping">survey_standard_dimension_question_mapping</a>

**说明：** 标准维度题目映射表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | standard_dimension_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准维度ID  |
|  2   | standard_question_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准题目ID  |
|  3   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_dimension_tenant_mapping">survey_standard_dimension_tenant_mapping</a>

**说明：** 标准维度和公司的映射表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | standard_dimension_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准维度ID  |
|  2   | tenant_id |   varchar   | 36 |   0    |    N     |  N   |       | 租户ID  |
|  3   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_norm">survey_standard_norm</a>

**说明：** 标准常模表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 常模名称  |
|  3   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  4   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  5   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  7   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  8   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_norm_question_mapping">survey_standard_norm_question_mapping</a>

**说明：** 标准prisma常模问题mapping

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | norm_id |   varchar   | 36 |   0    |    Y     |  N   |       | 常模ID  |
|  3   | question_code |   varchar   | 36 |   0    |    Y     |  N   |       | 问题编码  |
|  4   | value |   numeric   | 53 |   2    |    Y     |  N   |       | 常模值  |
|  5   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  6   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  8   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  9   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  10   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_norm_tenant_mapping">survey_standard_norm_tenant_mapping</a>

**说明：** 标准常模表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | standard_norm_id |   varchar   | 36 |   0    |    N     |  N   |       | 常模ID  |
|  2   | tenant_id |   varchar   | 36 |   0    |    N     |  N   |       | 租户ID  |
|  3   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_norm_value">survey_standard_norm_value</a>

**说明：** 标准常模值表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | standard_norm_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准常模ID  |
|  3   | standard_question_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准题目ID  |
|  4   | value |   varchar   | 100 |   0    |    Y     |  N   |       | 常模值  |
|  5   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  6   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  8   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  9   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  10   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_option">survey_standard_option</a>

**说明：** 选项表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |       | 调研类型  |
|  3   | question_type |   varchar   | 50 |   0    |    Y     |  N   |       | 题目类型单选多选量表  |
|  4   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 选项名称  |
|  5   | options |   json   | 2147483647 |   0    |    Y     |  N   |       | 选项值  |
|  6   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  7   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  8   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  9   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  10   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  11   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_option_introduction">survey_standard_option_introduction</a>

**说明：** 选项填答说明和规则

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | question_id |   varchar   | 36 |   0    |    Y     |  N   |       | 问题  |
|  3   | option_id |   varchar   | 36 |   0    |    Y     |  N   |       | 选项  |
|  4   | introduction |   text   | 2147483647 |   0    |    Y     |  N   |       | 选项介绍  |
|  5   | introduction_status |   varchar   | 10 |   0    |    Y     |  N   |   'DISABLE'::charactervarying    | 选项介绍是否启用  |
|  6   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  7   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  8   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  9   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  10   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  11   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_package">survey_standard_package</a>

**说明：** 标准常模表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 题目包名称  |
|  3   | algorithm_type |   varchar   | 50 |   0    |    Y     |  N   |       | 题目包算法类型  |
|  4   | algorithm |   json   | 2147483647 |   0    |    Y     |  N   |       | 题目包算法详情  |
|  5   | count |   int4   | 10 |   0    |    Y     |  N   |       | 题目包所含题目数  |
|  6   | question_type_filter |   varchar   | 50 |   0    |    Y     |  N   |       | 题目类型过滤枚举值量表迫选其他  |
|  7   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  9   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  10   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  11   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  12   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  13   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |   'ASSESSMENT'::charactervarying    | 调研类型  |

**表名：** <a id="survey_standard_package_question_mapping">survey_standard_package_question_mapping</a>

**说明：** 题目包题目映射表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | standard_package_id |   varchar   | 36 |   0    |    N     |  N   |       | 题目包ID  |
|  2   | standard_question_id |   varchar   | 36 |   0    |    N     |  N   |       | 标题题目ID  |
|  3   | standard_option_id |   varchar   | 36 |   0    |    Y     |  N   |       | 选项ID  |
|  4   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  5   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  6   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  8   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  9   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  10   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_prisma_norm">survey_standard_prisma_norm</a>

**说明：** 标准prisma常模表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 常模名称  |
|  3   | code |   varchar   | 36 |   0    |    Y     |  N   |       | 常模编码  |
|  4   | year |   varchar   | 10 |   0    |    Y     |  N   |       | 年份  |
|  5   | industry |   varchar   | 50 |   0    |    Y     |  N   |       | 行业  |
|  6   | quantile |   varchar   | 20 |   0    |    Y     |  N   |       | 分位  |
|  7   | amount |   numeric   | 131089 |   0    |    Y     |  N   |       | 常模价格，0表示免费  |
|  8   | status |   varchar   | 10 |   0    |    Y     |  N   |       | 状态  |
|  9   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  10   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  11   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  12   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  13   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  14   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_question">survey_standard_question</a>

**说明：** 标准题目表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 题目名称  |
|  3   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 题目类型  |
|  4   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |       | 调研类型  |
|  5   | timing |   int4   | 10 |   0    |    Y     |  N   |       | 题目计时  |
|  6   | level |   varchar   | 50 |   0    |    Y     |  N   |       | 题目难易长度简单中等困难  |
|  7   | is_require |   bool   | 1 |   0    |    Y     |  N   |       | 是否必填  |
|  8   | option_id |   varchar   | 36 |   0    |    Y     |  N   |       | 选项ID  |
|  9   | options_value |   json   | 2147483647 |   0    |    Y     |  N   |       | 选项值  |
|  10   | parent_id |   varchar   | 36 |   0    |    Y     |  N   |       | 题目父级ID为0代表父级  |
|  11   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  12   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  13   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  14   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  15   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  16   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  17   | label |   varchar   | 100 |   0    |    Y     |  N   |       | 标签  |
|  18   | sub_survey_type |   varchar   | 50 |   0    |    Y     |  N   |       | 子调研类型  |
|  19   | is_force_select |   bool   | 1 |   0    |    Y     |  N   |   true    | 是否必选  |
|  20   | code |   varchar   | 50 |   0    |    Y     |  N   |       | 题目编码  |
|  21   | description_status |   varchar   | 10 |   0    |    Y     |  N   |   'DISABLE'::charactervarying    | 是否启用说明  |
|  22   | description_match_condition |   varchar   | 10 |   0    |    Y     |  N   |       | 填答说明规则匹配条件  |
|  23   | description_rules |   text   | 2147483647 |   0    |    Y     |  N   |       | 填答说明匹配规则  |

**表名：** <a id="survey_standard_questionnaire">survey_standard_questionnaire</a>

**说明：** 标准问卷表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   varchar   | 100 |   0    |    N     |  N   |       | 问卷名称  |
|  3   | standard_report_template_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准报告模板ID  |
|  4   | code |   varchar   | 100 |   0    |    N     |  N   |       | 问卷code  |
|  5   | scope_type |   varchar   | 50 |   0    |    Y     |  N   |       | 标准问卷公开范围公开部分  |
|  6   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 标准问卷类型  |
|  7   | category_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准问卷分类ID  |
|  8   | is_service_statement |   bool   | 1 |   0    |    Y     |  N   |   false    | 标准问卷作答时间  |
|  9   | status |   varchar   | 50 |   0    |    Y     |  N   |   'ENABLE'::charactervarying    | 标准问卷状态  |
|  10   | question_num_in_one_page |   int4   | 10 |   0    |    N     |  N   |   5    | 标准问卷每页题数  |
|  11   | answer_description |   text   | 2147483647 |   0    |    Y     |  N   |       | 标准问卷作答说明  |
|  12   | duration |   int4   | 10 |   0    |    N     |  N   |       | 标准问卷作答时间  |
|  13   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  14   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  15   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  16   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  17   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  18   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  19   | report_type |   varchar   | 50 |   0    |    Y     |  N   |       | 问卷工具  |
|  20   | number_of_dimensions |   json   | 2147483647 |   0    |    Y     |  N   |       | 各维度题目数  |
|  21   | sequence |   varchar   | 50 |   0    |    Y     |  N   |   'QUESTION_TYPE'::charactervarying    | 排序方式  |
|  22   | is_draw_questions |   bool   | 1 |   0    |    Y     |  N   |       | 是否抽题  |
|  23   | normal_time_min |   int4   | 10 |   0    |    Y     |  N   |       | 正常时间最小值  |
|  24   | normal_time_max |   int4   | 10 |   0    |    Y     |  N   |       | 正常时间最大值  |
|  25   | color_config |   json   | 2147483647 |   0    |    Y     |  N   |       | 颜色配置  |
|  26   | is_show_organization |   bool   | 1 |   0    |    Y     |  N   |   false    | 是否显示组织架构  |
|  27   | is_show_question_book |   bool   | 1 |   0    |    Y     |  N   |   false    | 是否展示题本  |
|  28   | norm_code |   varchar   | 50 |   0    |    Y     |  N   |       | 常模编码  |
|  29   | optional_norm_codes |   json   | 2147483647 |   0    |    Y     |  N   |   '[]'::json    | 可选常模编码  |
|  30   | answer_description_bak |   text   | 2147483647 |   0    |    Y     |  N   |       |   |
|  31   | answer_description_bak2 |   text   | 2147483647 |   0    |    Y     |  N   |       |   |
|  32   | solution_type |   varchar   | 50 |   0    |    Y     |  N   |   'STANDARD_PRODUCT'::charactervarying    | 问卷方案  |
|  33   | type_id |   varchar   | 50 |   0    |    Y     |  N   |   '158341880269115403'::charactervarying    | 问卷类型  |
|  34   | is_standalone |   bool   | 1 |   0    |    Y     |  N   |   false    | 是否独立链接  |
|  35   | corner_mark |   varchar   | 50 |   0    |    Y     |  N   |   ''::charactervarying    | 问卷角标  |
|  36   | description |   text   | 2147483647 |   0    |    Y     |  N   |   ''::text    | 问卷说明  |

**表名：** <a id="survey_standard_questionnaire_category">survey_standard_questionnaire_category</a>

**说明：** 标准问卷分类表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   varchar   | 100 |   0    |    N     |  N   |       | 问卷分类名称  |
|  3   | code |   varchar   | 100 |   0    |    Y     |  N   |       | 问卷分类code  |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  10   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |   'ASSESSMENT'::charactervarying    | 调研类型  |

**表名：** <a id="survey_standard_questionnaire_dimension">survey_standard_questionnaire_dimension</a>

**说明：** 标准问卷维度表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 问卷维度名称  |
|  3   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 问卷维度来源类型标准维度自定义维度  |
|  4   | group_type |   varchar   | 50 |   0    |    Y     |  N   |       | 问卷维度类型子维度大维度  |
|  5   | standard_questionnaire_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准问卷ID  |
|  6   | standard_dimension_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准维度ID  |
|  7   | sort |   int4   | 10 |   0    |    Y     |  N   |       | 排序值  |
|  8   | code |   varchar   | 50 |   0    |    Y     |  N   |       |   |
|  9   | standard_package_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准题目包ID  |
|  10   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  11   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  12   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  13   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  14   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  15   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  16   | scope |   varchar   | 50 |   0    |    Y     |  N   |   'ALL'::charactervarying    | 适用对象全员普通员工高管  |
|  17   | level |   varchar   | 50 |   0    |    Y     |  N   |   'NONE'::charactervarying    | 层级  |

**表名：** <a id="survey_standard_questionnaire_dimension_mapping">survey_standard_questionnaire_dimension_mapping</a>

**说明：** 标准问卷维度映射表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | parent_id |   varchar   | 36 |   0    |    N     |  N   |       | 父级ID  |
|  2   | child_id |   varchar   | 36 |   0    |    N     |  N   |       | 子级ID  |
|  3   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_questionnaire_dimension_question_mapping">survey_standard_questionnaire_dimension_question_mapping</a>

**说明：** 标准问卷维度题目映射表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | standard_questionnaire_dimension_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准问卷维度ID  |
|  2   | standard_questionnaire_question_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准问卷题目ID  |
|  3   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_questionnaire_question">survey_standard_questionnaire_question</a>

**说明：** 标准问卷分类表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 问卷题目名称  |
|  3   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 问卷题目类型  |
|  4   | standard_questionnaire_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准问卷ID  |
|  5   | standard_question_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准题目ID  |
|  6   | is_require |   bool   | 1 |   0    |    Y     |  N   |       | 是否必填  |
|  7   | options_value |   json   | 2147483647 |   0    |    Y     |  N   |       | 题目选项值  |
|  8   | parent_id |   varchar   | 36 |   0    |    Y     |  N   |       | 父级ID为0代表父级  |
|  9   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  10   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  11   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  12   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  13   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  14   | sort |   int4   | 10 |   0    |    Y     |  N   |       |   |
|  15   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  16   | label |   varchar   | 100 |   0    |    Y     |  N   |       | 标签  |
|  17   | sub_survey_type |   varchar   | 50 |   0    |    Y     |  N   |       | 子调研类型  |
|  18   | is_force_select |   bool   | 1 |   0    |    Y     |  N   |   true    | 是否必选  |

**表名：** <a id="survey_standard_questionnaire_relation_permission">survey_standard_questionnaire_relation_permission</a>

**说明：** 标准问卷关联权限表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | standard_questionnaire_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准问卷id  |
|  3   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 权限类型  |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_questionnaire_scene_type">survey_standard_questionnaire_scene_type</a>

**说明：** 标准问卷场景类型表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   varchar   | 50 |   0    |    Y     |  N   |       | 名称  |
|  3   | type |   varchar   | 50 |   0    |    N     |  N   |       | 类型（标准产品、解决方案、产品类型）  |
|  4   | sort |   int4   | 10 |   0    |    Y     |  N   |   999    | 排序号  |
|  5   | is_default |   bool   | 1 |   0    |    Y     |  N   |   false    | 是否默认  |
|  6   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  7   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  8   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  9   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  10   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  11   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_questionnaire_scene_type_questionnaire_mapping">survey_standard_questionnaire_scene_type_questionnaire_mapping</a>

**说明：** 

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | scene_id |   varchar   | 36 |   0    |    N     |  N   |       | 场景id  |
|  3   | questionnaire_id |   varchar   | 36 |   0    |    N     |  N   |       | 问卷id  |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_questionnaire_tenant_mapping">survey_standard_questionnaire_tenant_mapping</a>

**说明：** 标准问卷分配公司表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | standard_questionnaire_id |   varchar   | 36 |   0    |    N     |  N   |       | 标准问卷ID  |
|  2   | tenant_id |   varchar   | 36 |   0    |    N     |  N   |       | 租户ID  |
|  3   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  4   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  5   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  6   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  9   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_sag_norm_transform">survey_standard_sag_norm_transform</a>

**说明：** 标准报告分值表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       | 分值  |
|  2   | type |   varchar   | 50 |   0    |    N     |  Y   |       | 报告类型  |
|  3   | low_tenths |   numeric   | 53 |   2    |    Y     |  N   |       | 十分质的最低分值  |
|  4   | high_tenths |   numeric   | 53 |   2    |    Y     |  N   |       | 十分质的最高分值  |
|  5   | table_name |   varchar   | 100 |   0    |    Y     |  N   |       | Ecexl表名  |
|  6   | code |   varchar   | 36 |   0    |    Y     |  N   |       | 报告编码  |
|  7   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  9   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  10   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  11   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  12   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_sag_report_dimension">survey_standard_sag_report_dimension</a>

**说明：** 报告维度

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度名字  |
|  3   | description |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度解释  |
|  4   | close_left_description |   json   | 2147483647 |   0    |    Y     |  N   |       | 分数靠左解释  |
|  5   | close_right_description |   json   | 2147483647 |   0    |    Y     |  N   |       | 分数靠右解释  |
|  6   | code |   varchar   | 50 |   0    |    Y     |  N   |       | 维度唯一编码  |
|  7   | close_left_describe_advantage |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度靠左优势描述  |
|  8   | close_left_describe_disadvantage |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度靠左劣势描述  |
|  9   | close_right_describe_advantage |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度靠右优势描述  |
|  10   | close_right_describe_disadvantage |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度靠右劣势描述  |
|  11   | sort |   int4   | 10 |   0    |    Y     |  N   |       | 维度排序  |
|  12   | table_name |   varchar   | 100 |   0    |    Y     |  N   |       | 表名  |
|  13   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  14   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  15   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  16   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  17   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  18   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  19   | reflective_question |   json   | 2147483647 |   0    |    Y     |  N   |       | 反思问题  |
|  20   | recommended_books |   json   | 2147483647 |   0    |    Y     |  N   |       | 推荐书籍  |
|  21   | interview_questions |   json   | 2147483647 |   0    |    Y     |  N   |       | 面试问题  |
|  22   | task_training |   json   | 2147483647 |   0    |    Y     |  N   |       | 任务训练  |
|  23   | recommended_courses |   json   | 2147483647 |   0    |    Y     |  N   |       | 推荐课程  |
|  24   | config_type |   varchar   | 100 |   0    |    Y     |  N   |       | 报告配置表配置类型  |

**表名：** <a id="survey_standard_sag_report_module">survey_standard_sag_report_module</a>

**说明：** 标准报告模块表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | key |   varchar   | 100 |   0    |    N     |  N   |       | 模块key  |
|  2   | parent_name |   varchar   | 100 |   0    |    Y     |  N   |       | 父模块名  |
|  3   | name |   varchar   | 100 |   0    |    Y     |  N   |       | 模块名  |
|  4   | edit |   varchar   | 100 |   0    |    Y     |  N   |       | 是否可编辑  |
|  5   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 报告模块类型  |
|  6   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  7   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  8   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  9   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  10   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  11   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  12   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_sag_report_score">survey_standard_sag_report_score</a>

**说明：** 标准报告分值表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 标准报告类型工作成就动机评测能力评测职业性格评测危机评测人才画像评测  |
|  3   | code |   varchar   | 50 |   0    |    Y     |  N   |       | 维度编码  |
|  4   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 维度名字  |
|  5   | high_score |   numeric   | 53 |   2    |    Y     |  N   |       | 分段高分值  |
|  6   | low_score |   numeric   | 53 |   2    |    Y     |  N   |       | 分段低分值  |
|  7   | description |   json   | 2147483647 |   0    |    Y     |  N   |       | 描述  |
|  8   | comment |   json   | 2147483647 |   0    |    Y     |  N   |       | 评语  |
|  9   | advantage |   json   | 2147483647 |   0    |    Y     |  N   |       | 优势  |
|  10   | development |   json   | 2147483647 |   0    |    Y     |  N   |       | 发展  |
|  11   | table_name |   varchar   | 100 |   0    |    Y     |  N   |       | 表名  |
|  12   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  13   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |       |   |
|  14   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |       |   |
|  15   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  16   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |       |   |
|  17   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |

**表名：** <a id="survey_standard_sag_report_template">survey_standard_sag_report_template</a>

**说明：** 标准报告模板表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 报告名称  |
|  3   | code |   varchar   | 50 |   0    |    Y     |  N   |       | 报告模板code  |
|  4   | sample_url |   varchar   | 100 |   0    |    Y     |  N   |       | 报告模板路径  |
|  5   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 报告类型(定制和标准)  |
|  6   | standard_report_type |   varchar   | 50 |   0    |    Y     |  N   |       | 报告类型（4种报告）  |
|  7   | standard_report_id |   varchar   | 36 |   0    |    Y     |  N   |       | 标准报告id，定制报告才有  |
|  8   | status |   varchar   | 50 |   0    |    Y     |  N   |       | 报告状态，新建默认OFF  |
|  9   | tenant_id |   varchar   | 36 |   0    |    Y     |  N   |       | 租户ID  |
|  10   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  11   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |       |   |
|  12   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |       |   |
|  13   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  14   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  15   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  16   | report_style |   varchar   | 36 |   0    |    Y     |  N   |       | 报告样式  |
|  17   | config_type |   varchar   | 36 |   0    |    Y     |  N   |       | 配置类型  |
|  18   | survey_type |   varchar   | 50 |   0    |    Y     |  N   |   'ASSESSMENT'::charactervarying    | 调研类型  |
|  19   | is_enable_job |   bool   | 1 |   0    |    Y     |  N   |   false    | 是否启用岗位  |
|  20   | model_type |   varchar   | 50 |   0    |    Y     |  N   |   'DYNAMIC'::charactervarying    | 报告模型类型  |
|  21   | model_image_url |   varchar   | 50 |   0    |    Y     |  N   |   ''::charactervarying    | 报告模型图片  |
|  22   | min_dimension_num |   int4   | 10 |   0    |    Y     |  N   |   1    | 最低维度数  |
|  23   | max_dimension_num |   int4   | 10 |   0    |    Y     |  N   |   30    | 最大维度数  |

**表名：** <a id="survey_standard_sag_report_template_config">survey_standard_sag_report_template_config</a>

**说明：** 标准报告模板配置表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | report_template_id |   varchar   | 36 |   0    |    Y     |  N   |       | 报告模板ID  |
|  3   | type |   varchar   | 50 |   0    |    Y     |  N   |       | 报告模块适应度详情详细得分适应度适应度水平封面总览概述  |
|  4   | data |   json   | 2147483647 |   0    |    Y     |  N   |       | 编辑模块数据key：模板名/模块名/付费，val：名字/数据/具体付费  |
|  5   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  6   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |       |   |
|  7   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |       |   |
|  8   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  9   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |       |   |
|  10   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  11   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 报告名  |
|  12   | style |   varchar   | 50 |   0    |    Y     |  N   |       | 报告样式  |
|  13   | dimension_config_type |   varchar   | 100 |   0    |    Y     |  N   |       | 报告配置表维度内容配置类型  |
|  14   | dimension_score_config_type |   varchar   | 100 |   0    |    Y     |  N   |       | 报告配置表维度分数配置类型  |

**表名：** <a id="survey_standard_sag_report_template_job">survey_standard_sag_report_template_job</a>

**说明：** 标准报告岗位表

**数据列：**

| 序号 | 名称 | 数据类型 |  长度  | 小数位 | 允许空值 | 主键 | 默认值 | 说明 |
| :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: | :---: |
|  1   | id |   varchar   | 36 |   0    |    N     |  Y   |       |   |
|  2   | report_template_id |   varchar   | 36 |   0    |    Y     |  N   |       | 报告模板id  |
|  3   | name |   json   | 2147483647 |   0    |    Y     |  N   |       | 岗位名称  |
|  4   | sort |   int4   | 10 |   0    |    Y     |  N   |   999    | 排序号  |
|  5   | create_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  6   | create_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  7   | update_time |   timestamp   | 26 |   3    |    Y     |  N   |   now()    |   |
|  8   | last_update_by |   varchar   | 36 |   0    |    Y     |  N   |       |   |
|  9   | is_deleted |   bool   | 1 |   0    |    Y     |  N   |   false    |   |
|  10   | app_id |   varchar   | 36 |   0    |    Y     |  N   |       |   |
