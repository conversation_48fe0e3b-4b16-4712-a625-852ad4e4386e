package pers.xdrodger.simple.test.ppt.poi.practice;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.sl.usermodel.TableCell;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xslf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.*;
import pers.xdrodger.util.ToolUtil;

import java.awt.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.security.InvalidParameterException;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PrismaPptUtil {

    private XMLSlideShow ppt;

    public static final int COLUMN_CATEGORY = 0;
    public static final String FORMAT_CODE_PERCENTAGE = "0.00%";
    public static final String FORMAT_CODE_GENERAL = "General";
    public static final String FORMAT_CODE_NUMBER = "0.00";

    public static final SlideLayout DEFAULT_SLIDE_LAYOUT = SlideLayout.TITLE_AND_CONTENT;

//    @Value("${knx.sag.report.print.pdfPath:/data/file/print/}")
    private String outputPath = FileUtil.getOutputFilePath();

    public PrismaPptUtil(String filePath) {
        try {
            loadFromPath(filePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public XMLSlideShow loadFromPath(String filePath) throws Exception {
        try {
            ClassPathResource classPathResource = new ClassPathResource(filePath);
            ppt = new XMLSlideShow(classPathResource.getInputStream());
            File dir = new File(outputPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            return ppt;
        } catch (Exception e) {
            FileInputStream fis = new FileInputStream(filePath);
            ppt = new XMLSlideShow(fis);
            return ppt;
        }
    }

    public String savePpt(String fileName) throws Exception {
        String filePath = outputPath + fileName + ".pptx";
        FileOutputStream out = new FileOutputStream(filePath);
        ppt.write(out);
        out.close();
        ppt.close();
        return filePath;
    }

    public List<XSLFShape> getShape(String shapeName) {
        List<XSLFShape> result = new ArrayList<>();
        List<XSLFSlide> slides = ppt.getSlides();
        for (XSLFSlide slide : slides) {
            List<XSLFShape> shapes = slide.getShapes();
            for (XSLFShape shape : shapes) {
                if (shape.getShapeName().equals(shapeName)) {
                    result.add(shape);
                }
            }
        }
        return result;
    }

    public XSLFTable getTable(XSLFSlide slide, String shapeName) {
        List<XSLFShape> shapes = slide.getShapes();
        for (XSLFShape shape : shapes) {
            if (shape instanceof XSLFTable && shape.getShapeName().equals(shapeName)) {
                return (XSLFTable) shape;
            }
        }
        log.error("shapeName={} not found in slideNumber={}", shapeName, slide.getSlideNumber());
        return null;
    }

    public XSLFChart getChart(XSLFSlide slide, String shapeName) {
        List<XSLFShape> shapes = slide.getShapes();
        for (XSLFShape shape : shapes) {
            if (shape instanceof XSLFGraphicFrame && !(shape instanceof XSLFTable) && shape.getShapeName().equals(shapeName)) {
                XSLFGraphicFrame graphicFrame = (XSLFGraphicFrame) shape;
                if (!graphicFrame.hasChart()) {
                    continue;
                }
                XSLFChart chart = graphicFrame.getChart();
                return chart;
            }
        }
        log.error("shapeName={} not found in slideNumber={}", shapeName, slide.getSlideNumber());
        return null;
    }


    public XSLFShape getShape(XSLFSlide slide, String shapeName) {
        List<XSLFShape> shapes = slide.getShapes();
        for (XSLFShape shape : shapes) {
            if (shape.getShapeName().equals(shapeName)) {
                return shape;
            }
        }
        log.error("shapeName={} not found in slideNumber={}", shapeName, slide.getSlideNumber());
        return null;
    }

    public XSLFSlide getSlideByShapeName(String shapeName) {
        List<XSLFSlide> slides = ppt.getSlides();
        for (XSLFSlide slide : slides) {
            List<XSLFShape> shapes = slide.getShapes();
            for (XSLFShape shape : shapes) {
                if (shape.getShapeName().equals(shapeName)) {
                    return slide;
                }
            }
        }
        throw new InvalidParameterException(shapeName);
    }

    public void replaceText(XSLFSlide slide, String shapeName, String value) {
        XSLFShape shape = getShape(slide, shapeName);
        if (shape == null) {
            return;
        }
        XSLFTextShape textShape = (XSLFTextShape) shape;
        List<XSLFTextParagraph> textParagraphs = textShape.getTextParagraphs();
        for (XSLFTextParagraph textParagraph : textParagraphs) {
            List<XSLFTextRun> textRuns = textParagraph.getTextRuns();
            for (XSLFTextRun textRun : textRuns) {
//                System.out.println(textRun.getRawText());
                textRun.setText(value);
            }
        }
    }

    public void replaceFirstTextAndRemoveOthers(XSLFSlide slide, String shapeName, String value) {
        XSLFShape shape = getShape(slide, shapeName);
        if (shape == null) {
            return;
        }
        XSLFTextShape textShape = (XSLFTextShape) shape;
        List<XSLFTextParagraph> textParagraphs = textShape.getTextParagraphs();
        removeOtherItemButFirst(textShape.getTextParagraphs());
        for (XSLFTextParagraph textParagraph : textParagraphs) {
            List<XSLFTextRun> textRuns = textParagraph.getTextRuns();
            removeOtherItemButFirst(textRuns);
            for (XSLFTextRun textRun : textRuns) {
                textRun.setText(value);
            }
        }
    }

    private void removeOtherItemButFirst(List<?> list) {
        Iterator<?> iterator = list.iterator();
        while (iterator.hasNext()) {
            int idx = list.indexOf(iterator.next());
            if (idx != 0) {
                iterator.remove();
            }
        }
    }

    private boolean isSameDataRangeReference(String dataRangeReference1, String dataRangeReference2) {
        String columnName1 = getColumnName(dataRangeReference1);
        String columnName2 = getColumnName(dataRangeReference2);
        return Objects.equals(columnName1, columnName2);
    }

    private String getColumnName(String dataRangeReference) {
        Pattern p = Pattern.compile("\\$[A-Z]\\$");
        Matcher matcher = p.matcher(dataRangeReference);
        if (matcher.find()) {
            String hitText = matcher.group();
            return hitText.replace("$", "");
        }
        throw new InvalidParameterException("");
    }

    public void replaceChartContent(XSLFSlide slide, PptChartData pptChartData) {
        XSLFChart chart = getChart(slide, pptChartData.getShapeName());
        if (chart == null) {
            return;
        }
        List<ChartCategorySeriesData> list = pptChartData.getCategorySeriesDataList();
        for (int i =0; i < list.size(); i ++) {
            ChartCategorySeriesData chartSeriesData = list.get(i);
            chartSeriesData.setColumn(i);
        }
        ChartCategorySeriesData categoryData = list.stream().filter(n -> n.getColumn() == COLUMN_CATEGORY).findFirst().orElse(null);
        if (categoryData == null) {
            throw new InvalidParameterException("");
        }
        if (StringUtils.isNotBlank(pptChartData.getTitle())) {
            chart.setTitleText(pptChartData.getTitle());
        }
        List<ChartCategorySeriesData> seriesDataList = list.stream().filter(n -> n.getColumn() != COLUMN_CATEGORY).collect(Collectors.toList());
        for (ChartCategorySeriesData categorySeriesData : list) {
            List<?> dataList = categorySeriesData.getDataList();
            String dataRange = chart.formatRange(new CellRangeAddress(1, dataList.size(), categorySeriesData.getColumn(), categorySeriesData.getColumn()));
            categorySeriesData.setDataRangeReference(dataRange);
            String titleRange = chart.formatRange(new CellRangeAddress(0, 0, categorySeriesData.getColumn(), categorySeriesData.getColumn()));
            categorySeriesData.setTitleRangeReference(titleRange);
            if (COLUMN_CATEGORY == categorySeriesData.getColumn()) {
                final XDDFCategoryDataSource categoriesData = XDDFDataSourcesFactory.fromArray(dataList.toArray(new String[0]), categorySeriesData.getDataRangeReference(), categorySeriesData.getColumn());
                categorySeriesData.setValueData(categoriesData);
            } else {
                List<Double> doubleDataList = new ArrayList<>();
                for (Object item : dataList) {
                    Double value = null;
                    if (item instanceof String) {
                        String strItem = (String) item;
                        if (strItem.contains("%")) {
                            value = new BigDecimal(Double.valueOf(strItem.replace("%", "")) / 100).setScale(2, BigDecimal.ROUND_UP).doubleValue();
                        } else {
                            value = Double.valueOf(strItem);
                        }
                    } else if (item instanceof Integer) {
                        value = Double.valueOf((Integer) item);
                    } else {
                        // pass
                        value = (Double) item;
                    }
                    doubleDataList.add(value);
                }
                final XDDFNumericalDataSource<? extends Number> valuesData = XDDFDataSourcesFactory.fromArray(doubleDataList.toArray(new Double[0]), categorySeriesData.getDataRangeReference(), categorySeriesData.getColumn());
                valuesData.setFormatCode(categorySeriesData.getFormatCode());
                categorySeriesData.setValueData(valuesData);
            }
        }
        final List<XDDFChartData> chartDataList = chart.getChartSeries();
        for (XDDFChartData chartData : chartDataList) {
            int seriesCount = chartData.getSeriesCount();
            for (int i = seriesCount - 1; i >= 0; i --) {
                XDDFChartData.Series series = chartData.getSeries(i);
                String dataRangeReference = series.getValuesData().getDataRangeReference();

                ChartCategorySeriesData chartSeriesData = seriesDataList.stream().filter(n -> isSameDataRangeReference(dataRangeReference, n.getDataRangeReference())).findFirst().orElse(null);
                if (chartSeriesData != null) {
                    String categoryRangeReference = series.getCategoryData().getDataRangeReference();
                    ChartCategorySeriesData categorySeriesData = seriesDataList.stream().filter(n -> isSameDataRangeReference(categoryRangeReference, n.getDataRangeReference())).findFirst().orElse(null);
                    if (categorySeriesData != null) {
                        series.replaceData(categorySeriesData.getValueData(), (XDDFNumericalDataSource<? extends Number>) chartSeriesData.getValueData());
                    } else {
                        series.replaceData(categoryData.getValueData(), (XDDFNumericalDataSource<? extends Number>) chartSeriesData.getValueData());
                    }
                    series.setTitle(chartSeriesData.getName(), new CellReference(chartSeriesData.getTitleRangeReference()));
                } else {
                    chartData.removeSeries(i);
                }
            }
            chart.plot(chartData);
        }
    }

    public void replaceTableContent(PptTableData pptTableData) {
        XSLFSlide defaultSlide = getSlideByShapeName(pptTableData.getShapeName());
        List<PptSlideData> slideDataList = new LinkedList<>();
        List<List<PptTableRowData>> rowDataGroups = ToolUtil.splitList(pptTableData.getBodyList(), pptTableData.getPageSplitSize() == 0 ? (pptTableData.getBodyList().size() + 1) : pptTableData.getPageSplitSize());
        int slideIndex = defaultSlide.getSlideNumber() - 1;
        for (int i=0; i < rowDataGroups.size(); i ++) {
            List<PptTableRowData> rowDataList = rowDataGroups.get(i);
            PptSlideData slideData = new PptSlideData();
            if (i == 0) {
                // pass
            } else {
                XSLFSlide newSlide = copySlide(slideIndex);
                slideIndex = newSlide.getSlideNumber() - 1;
            }
            PptTableData tableData = new PptTableData();
            BeanUtils.copyProperties(pptTableData, tableData);
            tableData.setBodyList(rowDataList);
            slideData.setTableData(tableData);
            slideData.setSlideIndex(slideIndex);
            slideDataList.add(slideData);
        }
        if (slideDataList.size() > 1) {
            tempSaveAndReload();
        }
        for (PptSlideData slideData : slideDataList) {
            XSLFSlide slide = ppt.getSlides().get(slideData.getSlideIndex());
            replaceTableContent(slide, slideData.getTableData());
        }
    }

    private void tempSaveAndReload() {
        try {
//            savePpt();
////            String filePath = outputPath + pd.getPrismaReportDataId() + "-" + language + ".pptx";
//            loadFromPathAndWait(filePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public XMLSlideShow loadFromPathAndWait(String filePath) throws Exception {
        File file = new File(filePath);
        while (!file.exists()) {
            System.out.println("文件读取中。。。");
        }
        ppt = new XMLSlideShow(new FileInputStream(file));
        return ppt;
    }


    public XSLFSlide copySlide(int index) {
        XSLFSlideLayout defaultSlideLayout = null;
        List<XSLFSlideMaster> slideMasters = ppt.getSlideMasters();
        for (XSLFSlideMaster slideMaster : slideMasters) {
            for (XSLFSlideLayout slideLayout : slideMaster.getSlideLayouts()) {
                if (Objects.equals(DEFAULT_SLIDE_LAYOUT, slideLayout.getType())) {
                    defaultSlideLayout = slideLayout;
                    break;
                }
            }
        }
        if (defaultSlideLayout == null) {
            throw new InvalidParameterException("");
        }
        XSLFSlide slide = ppt.getSlides().get(index);
        XSLFSlide newSlide = ppt.createSlide(defaultSlideLayout);
        newSlide.importContent(slide);
        ppt.setSlideOrder(newSlide, slide.getSlideNumber());
//        System.out.println("slideNumber=" + newSlide.getSlideNumber() + " index=" + ppt.getSlides().indexOf(newSlide));
        return newSlide;
    }

    public List<XSLFSlide> copySlide(int fromIndex, int toIndex) {
        XSLFSlideLayout defaultSlideLayout = null;
        List<XSLFSlideMaster> slideMasters = ppt.getSlideMasters();
        for (XSLFSlideMaster slideMaster : slideMasters) {
            for (XSLFSlideLayout slideLayout : slideMaster.getSlideLayouts()) {
                if (Objects.equals(DEFAULT_SLIDE_LAYOUT, slideLayout.getType())) {
                    defaultSlideLayout = slideLayout;
                    break;
                }
            }
        }
        if (defaultSlideLayout == null) {
            throw new InvalidParameterException("");
        }
        List<XSLFSlide> slides = new LinkedList<>();
        for (int i = fromIndex; i <= toIndex; i ++) {
            XSLFSlide slide = ppt.getSlides().get(i);
            XSLFSlide newSlide = ppt.createSlide(defaultSlideLayout).importContent(slide);
            ppt.setSlideOrder(newSlide, slide.getSlideNumber() + (toIndex - fromIndex));
//            System.out.println("slideNumber=" + newSlide.getSlideNumber() + " index=" + ppt.getSlides().indexOf(newSlide));
            slides.add(newSlide);
        }
        return slides;
    }
    

    public void replaceTableContent(XSLFSlide slide, PptTableData pptTableData) {
        XSLFTable table = getTable(slide, pptTableData.getShapeName());
        if (table != null) {
            replaceTableContent(table, pptTableData.getHeadList(), pptTableData.isNeedMergeHead(), pptTableData.getBodyList());
        }
    }

    public void replaceTableContent(XSLFTable table, List<?> head, boolean isNeedMergeHead, List<PptTableRowData> rowDataList) {
        syncTableRowNumAndCellNum3(table, head, rowDataList);
        List<XSLFTableRow> rows = getTableRows(table);
        if (head.size() > 0) {
            replaceTableHead(rows.get(0), head, isNeedMergeHead);
            replaceTableBody(rows.subList(1, rows.size()), rowDataList);
        } else {
            replaceTableBody(rows, rowDataList);
        }
    }

    private List<XSLFTableRow> getTableRows(XSLFTable table) {
        List<XSLFTableRow> rows = new ArrayList<>();
        Iterator<XSLFTableRow> iterator = table.iterator();
        while (iterator.hasNext()) {
            XSLFTableRow row = iterator.next();
            for (XSLFTableCell cell : row.getCells()) {
                System.out.println(cell.getText());
                for (XSLFTextParagraph textParagraph : cell.getTextParagraphs()) {
                    for (XSLFTextRun textRun : textParagraph.getTextRuns()) {
                        System.out.println(textRun.getRawText());
                    }
                }
            }
            rows.add(row);
        }
        return rows;
    }

    private void replaceTableHead(XSLFTableRow row, List<?> head, boolean isNeedMergeHead) {
        List<XSLFTableCell> cells = row.getCells();
        for (int j =0; j < cells.size(); j ++) {
            XSLFTableCell cell = cells.get(j);
            if (j >= head.size()) {
                continue;
            }
            if (head.get(j) == null) {
                // todo new version
                continue;
            }
            String replaceText = head.get(j) == null ? "" : String.valueOf(head.get(j));
            List<XSLFTextParagraph> textParagraphs = cell.getTextParagraphs();
            removeOtherItemButFirst(textParagraphs);
            for (XSLFTextParagraph textParagraph : textParagraphs) {
                textParagraph.clearTabStops();
                List<XSLFTextRun> textRuns = textParagraph.getTextRuns();
                removeOtherItemButFirst(textRuns);
                for (XSLFTextRun textRun : textRuns) {
                    textRun.setText(replaceText);
                }
            }
        }
        if (isNeedMergeHead) {
            int firstCol = 0;
            int lastCol = row.getCells().size()-1;
            if (lastCol > firstCol) {
                row.mergeCells(firstCol, lastCol);
            }
        }
    }

    private void replaceTableBody(List<XSLFTableRow> rows, List<PptTableRowData> rowDataList) {
        for (int i = 0; i < rows.size(); i ++) {
            XSLFTableRow row = rows.get(i);
            List<XSLFTableCell> cells = row.getCells();
            PptTableRowData rowData = rowDataList.get(i);
            List<PptTableCellData> cellDataList = rowData.getCellDataList();
//            System.out.println("row=" + (i + 1) + " cellSize=" + cells.size());
            for (int j =0; j < cells.size(); j ++) {
                XSLFTableCell cell = cells.get(j);
//                System.out.println(cell.getText());
                PptTableCellData cellData = cellDataList.get(j);
                if (cellData.isNeedFillColor() && cellData.getFillColor() != null) {
                    cell.setFillColor(cellData.getFillColor());
                }
                String replaceText = cellData.getValue() == null ? "" : String.valueOf(cellData.getValue());
                List<XSLFTextParagraph> textParagraphs = cell.getTextParagraphs();
                removeOtherItemButFirst(textParagraphs);
                for (XSLFTextParagraph textParagraph : textParagraphs) {
                    textParagraph.clearTabStops();
                    List<XSLFTextRun> textRuns = textParagraph.getTextRuns();
                    removeOtherItemButFirst(textRuns);
                    for (XSLFTextRun textRun : textRuns) {
                        textRun.setText(replaceText);
                    }
                }
            }
        }
    }

    private List<XSLFTableRow> syncTableRowNumAndCellNum3(XSLFTable table, List<?> head, List<PptTableRowData> rowDataList) {
        List<XSLFTableRow> rows = getTableRows(table);
        int rowSize = rows.size();
        int rowDataSize = head.isEmpty() ? rowDataList.size() : (rowDataList.size() + 1);
        if (rowDataSize > rowSize) {
            for (int i =rowSize; i < rowDataSize; i ++) {
                XSLFTableRow row = rows.get(rowSize -1);
                XSLFTableRow newRow = table.addRow();
                copyRow(row, newRow);
                rows.add(newRow);
            }
        } else if (rowDataSize < rowSize) {
            for (int i = rowSize - 1; i > rowDataSize -1; i --) {
                rows.remove(i);
                table.removeRow(i);
            }
        }
        XSLFTableRow lastRow = rows.get(rows.size() - 1);
        int cellSize = lastRow.getCells().size();
        PptTableRowData lastRowData = rowDataList.get(rowDataList.size() - 1);
        int cellDataSize = lastRowData.getCellDataList().size();
        if (cellDataSize > cellSize) {
//            for (int j = cellSize; j < cellDataSize; j ++) {
//                table.addColumn();
//            }
            List<XSLFTableRow> latestRows = getTableRows(table);
            Iterator<XSLFTableRow> iterator = table.iterator();
            while (iterator.hasNext()) {
                XSLFTableRow latestRow = iterator.next();
                for (int j = cellSize; j < cellDataSize; j++) {
                    XSLFTableCell newCell = latestRow.addCell();
//                    System.out.println(latestRow.getXmlObject().getTcList().get(cellSize -1).getTxBody().getPList().get(0).getRList().get(0).getT());
                    copyCell(latestRow.getCells().get(cellSize -1), newCell);
                }
            }
//            for (XSLFTableRow latestRow : latestRows) {
//                for (int j = cellSize; j < cellDataSize; j++) {
//                    XSLFTableCell newCell = latestRow.addCell();
//                    System.out.println(latestRow.getXmlObject().getTcList().get(cellSize -1).getTxBody().getPList().get(0).getRList().get(0).getT());
//                    copyCell(latestRow.getCells().get(cellSize -1), newCell);
//                }
//            }

        } else if (cellDataSize < cellSize) {
            for (int j = cellSize-1; j > cellDataSize-1; j --) {
                table.removeColumn(j);
            }
        }
        return rows;
    }

    private void copyRow(XSLFTableRow row, XSLFTableRow newRow) {
        newRow.setHeight(row.getHeight());
        List<XSLFTableCell> cells = row.getCells();
        List<XSLFTableCell> newCells = newRow.getCells();
//        Iterator<XSLFTableCell> iterator = newRow.iterator();
        for (int i = 0; i < cells.size(); i ++) {
            XSLFTableCell cell = cells.get(i);
            newRow.addCell();
            XSLFTableCell newCell = newCells.get(i);
            copyCell(cell, newCell);
        }
    }

    private void copyCell(XSLFTableCell cell, XSLFTableCell newCell) {
        newCell.setVerticalAlignment(cell.getVerticalAlignment());
        if (cell.getFillColor() == null) {
            newCell.setFillColor(Color.WHITE);
        } else {
            newCell.setFillColor(cell.getFillColor());
        }
        newCell.setTextDirection(cell.getTextDirection());
        newCell.setLeftInset(cell.getLeftInset());
        newCell.setInsets(cell.getInsets());
        newCell.setBottomInset(cell.getBottomInset());
        newCell.setRightInset(cell.getRightInset());
        newCell.setLeftInset(cell.getLeftInset());
        newCell.setTopInset(cell.getTopInset());
        for (TableCell.BorderEdge borderEdge : TableCell.BorderEdge.values()) {
            if (cell.getBorderColor(borderEdge) != null) {
                newCell.setBorderColor(borderEdge, cell.getBorderColor(borderEdge));
            }
            if (cell.getBorderWidth(borderEdge) != null) {
                newCell.setBorderWidth(borderEdge, cell.getBorderWidth(borderEdge));
            }
            if (cell.getBorderStyle(borderEdge) != null) {
                newCell.setBorderStyle(borderEdge, cell.getBorderStyle(borderEdge));
            }
            if (cell.getBorderCompound(borderEdge) != null) {
                newCell.setBorderCompound(borderEdge, cell.getBorderCompound(borderEdge));
            }
            if (cell.getBorderDash(borderEdge) != null) {
                newCell.setBorderDash(borderEdge, cell.getBorderDash(borderEdge));
            }
            if (cell.getBorderCap(borderEdge) != null) {
                newCell.setBorderCap(borderEdge, cell.getBorderCap(borderEdge));
            }

        }
        newCell.setLineColor(cell.getLineColor());
        newCell.setLineCompound(cell.getLineCompound());
        newCell.setLineCap(cell.getLineCap());
        newCell.setLineDash(cell.getLineDash());
        newCell.setLineWidth(cell.getLineWidth());
        newCell.setLineHeadLength(cell.getLineHeadLength());
        newCell.setLineTailLength(cell.getLineTailLength());
        List<XSLFTextParagraph> textParagraphs = cell.getTextParagraphs();
        for (XSLFTextParagraph textParagraph : textParagraphs) {
            XSLFTextParagraph newTextParagraph = newCell.addNewTextParagraph();
            newTextParagraph.setFontAlign(textParagraph.getFontAlign());
            newTextParagraph.setTextAlign(textParagraph.getTextAlign());
            newTextParagraph.setLeftMargin(textParagraph.getLeftMargin());
            newTextParagraph.setRightMargin(textParagraph.getRightMargin());
            newTextParagraph.setSpaceBefore(textParagraph.getSpaceBefore());
            newTextParagraph.setSpaceAfter(textParagraph.getSpaceAfter());
            newTextParagraph.setLineSpacing(textParagraph.getLineSpacing());
            newTextParagraph.setIndent(textParagraph.getIndent());
            newTextParagraph.setIndentLevel(textParagraph.getIndentLevel());
            for (XSLFTextRun textRun : textParagraph.getTextRuns()) {
                XSLFTextRun newTextFun = newTextParagraph.addNewTextRun();
                newTextFun.setText(textRun.getRawText());
                newTextFun.setFontColor(textRun.getFontColor());
                newTextFun.setFontFamily(textRun.getFontFamily());
                newTextFun.setBold(textRun.isBold());
                newTextFun.setFontSize(textRun.getFontSize());
                newTextFun.setItalic(textRun.isItalic());
            }
        }
    }

}
