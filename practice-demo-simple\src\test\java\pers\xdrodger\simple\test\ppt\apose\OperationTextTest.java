package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableCellData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableRowData;

import java.awt.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Random;

public class OperationTextTest {

    @Test
    public void testRead() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-赞成度表.pptx");
        ITable table = prismaPptUtil.getTable("eei-new-table");
        int rowCount = table.getTableRows().getCount();
        int colCount = table.getColumnsList().getCount();
        for (int i = 0; i < rowCount; i ++) {
            for (int j =0; j < colCount; j ++) {
                Cell cell = table.get(j, i);
                System.out.println(cell.getTextFrame().getText());
            }
        }
    }

    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil pptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-删除文本框.pptx");
        pptUtil.replaceText("p3-eei-esi-demographic-h-h-text", "");
        pptUtil.removeShape("p3-eei-esi-demographic-h-h-text");
        pptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-删除文本框.pptx");
    }
}