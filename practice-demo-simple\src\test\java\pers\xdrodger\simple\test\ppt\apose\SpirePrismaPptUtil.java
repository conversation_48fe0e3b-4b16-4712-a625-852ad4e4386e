package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataTable;
import com.spire.ms.System.Collections.IEnumerator;
import com.spire.presentation.*;
import com.spire.presentation.charts.*;
import com.spire.presentation.charts.entity.ChartDataLabel;
import com.spire.presentation.drawing.FillFormatType;
import com.spire.presentation.drawing.IImageData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.*;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Component
public class SpirePrismaPptUtil {


    private Presentation ppt = new Presentation();

    public SpirePrismaPptUtil(String filePath) {
        try {
            loadFromPath(filePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public SpirePrismaPptUtil() {

    }

    public void loadFromPath(String filePath) throws Exception {
        if (filePath.startsWith("files/")) {
            ClassPathResource classPathResource = new ClassPathResource(filePath);
            ppt.loadFromStream(classPathResource.getInputStream(), FileFormat.PPTX_2013);
        } else {
            ppt.loadFromFile(filePath);
        }
    }

    public Presentation getPpt() {
        return ppt;
    }

    public ISlide getFirstSlide(String shapeName) {
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                return slide;
            }
        }
        log.error("shapeName={} not fount", shapeName);
        return null;
    }

    public IShape getShape(String shapeName) {
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                return shape;
            }
        }
        log.error("shapeName={} not fount", shapeName);
        return null;
    }

    public List<IShape> getShapes(String shapeName) {
        List<IShape> shapes = new ArrayList<>();
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                shapes.add(shape);
            }
        }
        return shapes;
    }

    public IShape getShape(ISlide slide, String shapeName) {
        ShapeCollection shapeCollection = slide.getShapes();
        Iterator<IShape> iterator = shapeCollection.iterator();
        while (iterator.hasNext()) {
            IShape shape = iterator.next();
            // 组合图形
            if (shape instanceof GroupShape) {
                IShape innerShape = getShapeFromGroupShape((GroupShape) shape, shapeName);
                if (innerShape != null) {
                    return innerShape;
                }
            }
            if (shape.getName().equals(shapeName)) {
                return shape;
            }
        }
        return null;
    }

    private IShape getShapeFromGroupShape(GroupShape groupShape, String shapeName) {
        Iterator<IShape> iterator = groupShape.getShapes().iterator();
        while (iterator.hasNext()) {
            IShape innerShape = iterator.next();
            if (innerShape.getName().equals(shapeName)) {
                return innerShape;
            }
            // 内部图形还是租户图形，递归处理
            if (innerShape instanceof GroupShape) {
                IShape shape = getShapeFromGroupShape((GroupShape) innerShape, shapeName);
                if (shape != null) {
                    return shape;
                }
            }
        }
        return null;
    }

    public IChart getChart(String shapeName) {
        IShape shape = getShape(shapeName);
        if (shape != null) {
            return (IChart) shape;
        }
        return null;
    }

    public IChart getChart(ISlide slide, String shapeName) {
        IShape shape = getShape(slide, shapeName);
        if (shape != null) {
            return (IChart) shape;
        }
        log.error("shapeName={} not fount", shapeName);
        return null;
    }

    public ITable getTable(String shapeName) {
        IShape shape = getShape(shapeName);
        if (shape != null) {
            return (ITable) shape;
        }
        return null;
    }

    public ITable getTable(ISlide slide, String shapeName) {
        IShape shape = getShape(slide, shapeName);
        if (shape != null) {
            return (ITable) shape;
        }
        log.error("shapeName={} not fount", shapeName);
        return null;
    }

    public SlidePicture getSlidePicture(String shapeName) {
        IShape shape = getShape(shapeName);
        if (shape != null) {
            return (SlidePicture) shape;
        }
        return null;
    }

    public void replaceTable(PptTableData pptTableData) {
        ISlide slide = getFirstSlide(pptTableData.getShapeName());
        replaceTable(slide, pptTableData);
    }

    public void replaceTable(ISlide slide, PptTableData pptTableData) {
        log.info("replace table {}", pptTableData.getShapeName());
        List<?> headList = pptTableData.getHeadList();
        List<PptTableRowData> bodyList = pptTableData.getBodyList();
        if (bodyList.size() == 0) {
            return;
        }
        ITable table = getTable(slide, pptTableData.getShapeName());
        if (table == null) {
            return;
        }
        try {
            syncTableRowNumAndCellNum(table, headList, bodyList);
            int rowCount = table.getTableRows().getCount();
            int colCount = table.getColumnsList().getCount();
            for (int i = 0; i < rowCount; i ++) {
                for (int j =0; j < colCount; j ++) {
                    Cell cell = table.get(j, i);
                    if (i == 0) {
                        String value;
                        if (headList.size() > 0) {
                            value = (String) headList.get(j);
                        } else {
                            value = (String) bodyList.get(0).getCellDataList().get(j).getValue();
                        }
                        if (value == null) {
                            value = "";
                        }
                        cell.getTextFrame().setText(value);
                    } else {
                        String stringValue;
                        PptTableCellData cellData = bodyList.get(i -1).getCellDataList().get(j);
                        Object value = cellData.getValue();
                        if (value == null) {
                            stringValue = "";
                        } else if (value instanceof Double || value instanceof Integer || value instanceof Float ) {
                            stringValue = value.toString();
                        } else {
                            stringValue =(String) value;
                        }
                        cell.getTextFrame().setText((stringValue));
                        if (cellData.getFillColor() != null) {
                            setCellFillColor(cell, cellData.getFillColor());
                        }
                        if (cellData.getFontColor() != null) {
                            setCellFontColor(cell, cellData.getFontColor());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("replace table {} fail, msg={}", pptTableData.getShapeName(), e.getMessage(), e);
//            throw new BusinessException(SurveyErrorCode.OPERATION_FAIL);
        }
    }

    public void setCellFillColor(Cell cell, Color fillColor) {
        cell.getFillFormat().setFillType(FillFormatType.SOLID);
        cell.getFillFormat().getSolidColor().setColor(fillColor);
    }

    public void setCellFontColor(Cell cell, Color fontColor) {
        cell.getFillFormat().setFillType(FillFormatType.NONE);
        cell.getTextFrame().getTextRange().getFill().setFillType(FillFormatType.SOLID);
        cell.getTextFrame().getTextRange().getFill().getSolidColor().setColor(fontColor);
    }

    private void syncTableRowNumAndCellNum(ITable table, List<?> headList, List<PptTableRowData> bodyList) throws Exception {
        int newColCount = headList.isEmpty() ? bodyList.get(0).getCellDataList().size() : headList.size();
        int newRowCount = headList.isEmpty() ? bodyList.size() : (bodyList.size() + 1);
        int rowCount = table.getTableRows().getCount();
        int colCount = table.getColumnsList().getCount();
        if (newColCount > colCount) {
            for (int j = colCount; j < newColCount; j ++) {
                TableColumn column =table.getColumnsList().get(colCount-1);
                table.getColumnsList().add(column);
            }
        } else if (newColCount < colCount) {
            for (int j = colCount-1; j > newColCount-1; j --) {
                table.getColumnsList().removeAt(j, false);
            }
        }

        if (newRowCount > rowCount) {
            for (int i =rowCount; i < newRowCount; i ++) {
                TableRow row = table.getTableRows().get(rowCount - 1);
                table.getTableRows().append(row);
            }
        } else if (newRowCount < rowCount) {
            for (int i = rowCount - 1; i > newRowCount -1; i --) {
                table.getTableRows().removeAt(i, false);
            }
        }
    }

    public void replaceChart(PptChartData pptChartData) {
        ISlide slide = getFirstSlide(pptChartData.getShapeName());
        replaceChart(slide, pptChartData);
    }

    public void replaceChart(ISlide slide, PptChartData pptChartData) {
        log.info("replace chart {}", pptChartData.getShapeName());
        try {
            IChart chart = getChart(slide, pptChartData.getShapeName());
            DataTable dataTable = pptChartData.getDataTable();
            int colCount = dataTable.getColumns().size();
            int rowCount = dataTable.getRows().size();
            chart.getChartData().clear(0, 0, 20, 10);
            //将数据写入图表
            for (int c = 0; c < colCount; c++) {
                chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
            }
            for (int r = 0; r < rowCount; r++) {
                Object[] datas = dataTable.getRows().get(r).getArrayList();
                for (int c = 0; c < datas.length; c++) {
                    chart.getChartData().get(r + 1, c).setValue(datas[c]);

                }
            }
            // 设置类别标签（第二行以后，第一列的数据, A2-AN）
            String categoryNameCellBegin = digitToCapitalLetter(0) + "2";
            String categoryNameCellEnd = digitToCapitalLetter(0) + (rowCount + 1);
            log.info("replace chart {} category name cell begin={} end={}", pptChartData.getShapeName(), categoryNameCellBegin, categoryNameCellEnd);
            chart.getCategories().setCategoryLabels(chart.getChartData().get( categoryNameCellBegin, categoryNameCellEnd));
            // 设置系列标签（第一行，第二列以后的数据，B1-Z1）
            String seriesNameCellBegin = digitToCapitalLetter(1) + "1";
            String seriesNameCellEnd = digitToCapitalLetter(colCount - 1) + "1";
            log.info("replace chart {} series name cell begin={} end={}", pptChartData.getShapeName(), seriesNameCellBegin, seriesNameCellEnd);
            chart.getSeries().setSeriesLabel(chart.getChartData().get( seriesNameCellBegin, seriesNameCellEnd));
            for (int i = 0; i < colCount - 1; i ++) {
                // 为各个系列赋值
                String seriesValueBegin = digitToCapitalLetter(i + 1) + "2";
                String seriesValueEnd = digitToCapitalLetter(i + 1) + (rowCount + 1);
                log.info("replace chart {} series value cell begin={} end={}", pptChartData.getShapeName(), seriesValueBegin, seriesValueEnd);
                chart.getSeries().get(i).setValues(chart.getChartData().get(seriesValueBegin, seriesValueEnd));
            }
            // 设置各个系列的图表类型和颜色
            List<ChartCategorySeriesData> seriesDataList = pptChartData.getCategorySeriesDataList();
            for (int i =0; i < seriesDataList.size(); i ++) {
                ChartCategorySeriesData seriesData = seriesDataList.get(i);
                if (seriesData.getColor() != null) {
                    chart.getSeries().get(i).getFill().setFillType(FillFormatType.SOLID);
                    chart.getSeries().get(i).getFill().getSolidColor().setColor(Color.BLUE);
                }
                if (seriesData.getChartType() != null) {
                    chart.getSeries().get(i).setType(seriesData.getChartType());
                }
            }
            List<Color> approvalColors = new ArrayList<>();
            approvalColors.add(new Color(35 ,100, 160));
            approvalColors.add(new Color(155 ,187 ,89));
            approvalColors.add(new Color(192 ,80, 77));

            List<Color> barColors = new ArrayList<>();
            barColors.add(new Color(91 ,155, 213));
            barColors.add(new Color(237 ,125 ,49));
            barColors.add(new Color(165 ,165, 165));

            List<Color> radarColors = new ArrayList<>();
            radarColors.add(new Color(35 ,100, 160));
            radarColors.add(new Color(237 ,125 ,49));
            radarColors.add(new Color(165 ,165, 165));
            for (int i = 0; i < colCount - 1; i ++) {
                List<String> approvalChartList = approvalChartList();
                if (approvalChartList.contains(pptChartData.getShapeName())) {
                    if (approvalColors.size() >= i+1) {
                        chart.getSeries().get(i).getFill().setFillType(FillFormatType.SOLID);
                        chart.getSeries().get(i).getFill().getSolidColor().setColor(approvalColors.get(i));
                    }
                    for (int j =0; j < dataTable.getRows().size(); j ++) {
                        ChartDataLabel dataLabel = chart.getSeries().get(i).getDataLabels().add();
                        //显示标签的值
                        dataLabel.setLabelValueVisible(true);
                        dataLabel.setPosition(ChartDataLabelPosition.CENTER);
                    }
                }
                if (pptChartData.getShapeName().contains("radar-map-chart")) {
                    if (i == 0 && radarColors.size() >= 1) {
                        chart.getSeries().get(i).setType(ChartType.RADAR_FILLED);
                        chart.getSeries().get(i).getFill().setFillType(FillFormatType.SOLID);
                        chart.getSeries().get(i).getFill().getSolidColor().setColor(radarColors.get(i));
                    } else {
                        // 使用次坐标轴
                        chart.getSeries().get(i).setUseSecondAxis(true);
                        chart.getSeries().get(i).setType(ChartType.RADAR);

                        if (radarColors.size() >= i+1) {
                            chart.getSeries().get(i).getLine().setFillType(FillFormatType.SOLID);
                            chart.getSeries().get(i).getLine().getSolidFillColor().setColor(radarColors.get(i));
                        }

                    }
                }
                if ("p2-overview-esi-parent-dimension-bar-chart".equals(pptChartData.getShapeName())
                        || "p2-overview-esi-department-n-parent-dimension-bar-chart".equals(pptChartData.getShapeName())) {
                    if (barColors.size() >= i+1) {
                        chart.getSeries().get(i).getFill().setFillType(FillFormatType.SOLID);
                        chart.getSeries().get(i).getFill().getSolidColor().setColor(barColors.get(i));
                    }
                }
                if (pptChartData.getShapeName().contains("answer-chart")) {
                    for (int j =0; j < dataTable.getRows().size(); j ++) {
                        ChartDataLabel dataLabel = chart.getSeries().get(i).getDataLabels().add();
                        //显示标签的值
                        dataLabel.setLabelValueVisible(true);
                        dataLabel.setPercentageVisible(true);
                    }
                }
            }

            //  设置图表名称
            if (StringUtils.isNotBlank(pptChartData.getTitle())) {
                chart.getChartTitle().getTextProperties().setText(pptChartData.getTitle());
            }

        } catch (Exception e) {
            log.error("replace chart {} fail, msg={}", pptChartData.getShapeName(), e.getMessage(), e);
//            throw new BusinessException(SurveyErrorCode.OPERATION_FAIL);
        }
    }

    public void replaceScatterChart(PptChartData pptChartData) {
        log.info("replace chart {}", pptChartData.getShapeName());
        try {
            IChart chart = getChart(pptChartData.getShapeName());
            DataTable dataTable = pptChartData.getDataTable();
            int colCount = dataTable.getColumns().size();
            int rowCount = dataTable.getRows().size();
            chart.getChartData().clear(0, 0, 20, 10);
            //将数据写入图表
            for (int c = 0; c < colCount; c++) {
                chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
            }
            for (int r = 0; r < rowCount; r++) {
                Object[] datas = dataTable.getRows().get(r).getArrayList();
                for (int c = 0; c < datas.length; c++) {
                    chart.getChartData().get(r + 1, c).setValue(datas[c]);

                }
            }
            //设置系列标签
            chart.getSeries().setSeriesLabel(chart.getChartData().get("F1", "H1"));
            // 散点
            chart.getSeries().get(0).setXValues(chart.getChartData().get("E2", "E8"));
            chart.getSeries().get(0).setYValues(chart.getChartData().get("D2", "D8"));
            // 误差线 分析主体
            chart.getSeries().get(1).setXValues(chart.getChartData().get("G2", "G2"));
            chart.getSeries().get(1).setYValues(chart.getChartData().get("G3", "G3"));

            IErrorBarsFormat xErrorBarsFormat = chart.getSeries().get(1).getErrorBarsXFormat();
            xErrorBarsFormat.setErrorBarvType(ErrorValueType.CUSTOM_ERROR_BARS.getValue());
            xErrorBarsFormat.setErrorBarSimType(ErrorBarSimpleType.BOTH.getValue());
            xErrorBarsFormat.setErrorBarNoEndCap(true);
            xErrorBarsFormat.setPlusVal((Float) chart.getChartData().get("G3", "G3").get(0).getValue());
            xErrorBarsFormat.setMinusVal((Float) chart.getChartData().get("G2", "G2").get(0).getValue());
            xErrorBarsFormat.getLine().setDashStyle(LineDashStyleType.DASH);
            xErrorBarsFormat.getLine().setWidth(1);
            xErrorBarsFormat.getLine().setFillType(FillFormatType.SOLID);
            xErrorBarsFormat.getLine().getSolidFillColor().setColor(Color.ORANGE);

            IErrorBarsFormat yErrorBarsFormat = chart.getSeries().get(1).getErrorBarsYFormat();
            yErrorBarsFormat.getLine().setDashStyle(LineDashStyleType.DASH);
            yErrorBarsFormat.getLine().setFillType(FillFormatType.SOLID);
            yErrorBarsFormat.getLine().getSolidFillColor().setColor(Color.ORANGE);
            yErrorBarsFormat.getLine().setWidth(1);
            yErrorBarsFormat.setErrorBarvType(ErrorValueType.CUSTOM_ERROR_BARS.getValue());
            yErrorBarsFormat.setErrorBarSimType(ErrorBarSimpleType.BOTH.getValue());
            yErrorBarsFormat.setErrorBarNoEndCap(true);
            yErrorBarsFormat.setPlusVal((Float) chart.getChartData().get("G2", "G2").get(0).getValue());
            yErrorBarsFormat.setMinusVal((Float) chart.getChartData().get("G3", "G3").get(0).getValue());

            // 误差线 常模
            chart.getSeries().get(2).setXValues(chart.getChartData().get("H2", "H2"));
            chart.getSeries().get(2).setYValues(chart.getChartData().get("H3", "H3"));

            IErrorBarsFormat xErrorBarsFormat2 = chart.getSeries().get(2).getErrorBarsXFormat();
            xErrorBarsFormat2.getLine().setDashStyle(LineDashStyleType.DASH);
            xErrorBarsFormat2.getLine().setFillType(FillFormatType.SOLID);
            xErrorBarsFormat2.getLine().getSolidFillColor().setColor(Color.GRAY);
            xErrorBarsFormat2.getLine().setWidth(1);
            xErrorBarsFormat2.setErrorBarvType(ErrorValueType.CUSTOM_ERROR_BARS.getValue());
            xErrorBarsFormat2.setErrorBarSimType(ErrorBarSimpleType.BOTH.getValue());
            xErrorBarsFormat2.setErrorBarNoEndCap(true);
            xErrorBarsFormat2.setPlusVal((Float) chart.getChartData().get("H3", "H3").get(0).getValue());
            xErrorBarsFormat2.setMinusVal((Float) chart.getChartData().get("H2", "H2").get(0).getValue());

            IErrorBarsFormat yErrorBarsFormat2 = chart.getSeries().get(2).getErrorBarsYFormat();
            yErrorBarsFormat2.getLine().setDashStyle(LineDashStyleType.DASH);
            yErrorBarsFormat2.getLine().setFillType(FillFormatType.SOLID);
            yErrorBarsFormat2.getLine().getSolidFillColor().setColor(Color.GRAY);
            yErrorBarsFormat2.getLine().setWidth(1);
            yErrorBarsFormat2.setErrorBarvType(ErrorValueType.CUSTOM_ERROR_BARS.getValue());
            yErrorBarsFormat2.setErrorBarSimType(ErrorBarSimpleType.BOTH.getValue());
            yErrorBarsFormat2.setErrorBarNoEndCap(true);
            yErrorBarsFormat2.setPlusVal((Float) chart.getChartData().get("H2", "H2").get(0).getValue());
            yErrorBarsFormat2.setMinusVal((Float) chart.getChartData().get("H3", "H3").get(0).getValue());


            //  设置图表名称
            if (StringUtils.isNotBlank(pptChartData.getTitle())) {
                chart.getChartTitle().getTextProperties().setText(pptChartData.getTitle());
            }

        } catch (Exception e) {
            log.error("replace chart {} fail, msg={}", pptChartData.getShapeName(), e.getMessage(), e);
//            throw new BusinessException(SurveyErrorCode.OPERATION_FAIL);
        }
    }

    private List<String> approvalChartList() {
        return Arrays.asList(new String[]{
                "p2-overview-eei-question-chart", "p2-overview-eei-department-n-question-chart",
                "p2-overview-oci-question-chart", "p2-overview-oci-department-n-question-chart",
                "p2-overview-esi-parent-dimension-chart", "p2-overview-esi-department-n-parent-dimension-chart",
                "p2-overview-esi-dimension-chart", "p2-overview-esi-department-n-dimension-chart",
                "p6-eei-dimension-question-chart", "p6-esi-parent-dimension-chart", "p6-esi-dimension-question-chart"
        });
    }

    private String digitToCapitalLetter(int digit) {
        return String.valueOf((char) (digit + 65));
    }

    public void replaceText(String shapeName, String newText) {
        ISlide slide = getFirstSlide(shapeName);
        if (slide != null) {
            replaceText(slide, shapeName, newText);
        }
        log.error("replace text {} not found", shapeName);
    }

    public void replaceAllText(String shapeName, String newText) {
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                replaceText(slide, shapeName, newText);
            }
        }

    }

    public void replaceText(ISlide slide, String shapeName, String newText) {
        log.info("replace text {}", shapeName);
        try {
            IShape shape = getShape(slide, shapeName);
            IAutoShape autoShape = (IAutoShape) shape;
            autoShape.getTextFrame().setText(newText);
        } catch (Exception e) {
            log.error("replace text {} fail, msg={}", shapeName, e.getMessage(), e);
//            throw new BusinessException(SurveyErrorCode.OPERATION_FAIL);
        }
    }

    public void replacePicture(String shapeName, String filePath) {
        try {
            SlidePicture picture = getSlidePicture(shapeName);
            BufferedImage bufferedImage = ImageIO.read( new FileInputStream( filePath));
            IImageData imageData = ppt.getImages().append(bufferedImage);
            picture.getPictureFill().getPicture().setEmbedImage(imageData);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void replaceTenantLogo(String shapeName, String filePath) {
        try {
            SlidePicture picture = getSlidePicture(shapeName);
            BufferedImage bufferedImage = ImageIO.read( new FileInputStream( filePath));
            IImageData imageData = ppt.getImages().append(bufferedImage);
            picture.getPictureFill().getPicture().setEmbedImage(imageData);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void removeShape(String shapeName) {
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                slide.getShapes().remove(shape);
                return;
            }
        }
    }

    public void removeAllShape(String shapeName) {
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                slide.getShapes().remove(shape);
            }
        }
    }

    public void removeSlide(String shapeName) {
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                ppt.getSlides().remove(slide);
                return;
            }
        }
    }

    public void removeAllSlide(String shapeName) {
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        for (int i = ppt.getSlides().getCount() - 1; i >= 0; i --) {
            ISlide slide = ppt.getSlides().get(i);
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                ppt.getSlides().remove(slide);
            }
        }
    }

    public ISlide copySlide(int slideIndex) throws Exception {
        ISlide slide = ppt.getSlides().get(slideIndex);
        int index = slideIndex + 1;
        ppt.getSlides().insert(index, slide);
        return ppt.getSlides().get(index);
    }

    public String savePpt(String fileName) throws Exception {
        //保存文件
        ppt.saveToFile(fileName, FileFormat.PPTX_2013);
        return fileName;
    }
}
