package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import com.spire.presentation.drawing.BackgroundType;
import com.spire.presentation.drawing.FillFormatType;
import com.spire.presentation.drawing.IImageData;
import com.spire.presentation.drawing.PictureFillType;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;

public class CreateMultipleMasterSlide {
    public static void main(String[] args) throws Exception{
        //创建PPT文档，并设置幻灯片大小
        Presentation ppt = new Presentation();
        ppt.getSlideSize().setType(SlideSizeType.SCREEN_16_X_9);

        //插入4页幻灯片(连同默认的幻灯片，文档中共5页)
        for (int i = 0; i < 4; i++)
        {
            ppt.getSlides().append();
        }

        //获取默认的母版
        IMasterSlide first_master = ppt.getMasters().get(0);

        //创建并获取第二个母板
        ppt.getMasters().appendSlide(first_master);
        IMasterSlide second_master = ppt.getMasters().get(1);

        //为两个母版分别设置不同的背景图片
        BufferedImage image = ImageIO.read(new FileInputStream("D:\\workspace\\practice-demo\\file\\input\\tp3.jpeg"));
        IImageData imageData = ppt.getImages().append(image);
        first_master.getSlideBackground().setType(BackgroundType.CUSTOM);
        first_master.getSlideBackground().getFill().setFillType(FillFormatType.PICTURE);
        first_master.getSlideBackground().getFill().getPictureFill().setFillType(PictureFillType.STRETCH);
        first_master.getSlideBackground().getFill().getPictureFill().getPicture().setEmbedImage(imageData);
        IAutoShape textShape = first_master.getShapes().appendShape(ShapeType.RECTANGLE, new Rectangle2D.Float((float) ppt.getSlideSize().getSize().getWidth()/3,180,200,30));
        textShape.getTextFrame().setText("首页母版");
        textShape.getTextFrame().getTextRange().setFontHeight(40f);
        textShape.getTextFrame().getTextRange().getFill().setFillType(FillFormatType.SOLID);
        textShape.getTextFrame().getTextRange().getFill().getSolidColor().setColor(Color.red);
        textShape.getTextFrame().getTextRange().getParagraph().setAlignment(TextAlignmentType.CENTER);
        textShape.getFill().setFillType(FillFormatType.NONE);
        textShape.getLine().setFillType(FillFormatType.NONE);

        image = ImageIO.read(new FileInputStream("D:\\workspace\\practice-demo\\file\\input\\tp2.jpeg"));
        imageData = ppt.getImages().append(image);
        second_master.getSlideBackground().setType(BackgroundType.CUSTOM);
        second_master.getSlideBackground().getFill().setFillType(FillFormatType.PICTURE);
        second_master.getSlideBackground().getFill().getPictureFill().setFillType(PictureFillType.STRETCH);
        second_master.getSlideBackground().getFill().getPictureFill().getPicture().setEmbedImage(imageData);

        //在第一页应用第一个母版及版式（板式6为空板式）
        ppt.getSlides().get(0).setLayout(first_master.getLayouts().get(6));

        //在剩下的幻灯片应用第二个母版及版式
        for (int i = 1; i < ppt.getSlides().getCount(); i++)
        {
            ppt.getSlides().get(i).setLayout(second_master.getLayouts().get(6));
        }

        //保存文档
        ppt.saveToFile("D:\\workspace\\practice-demo\\file\\output\\MultiSlideMaters.pptx", FileFormat.PPTX_2013);
        ppt.dispose();
    }
}