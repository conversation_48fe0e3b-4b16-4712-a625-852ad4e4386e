package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.charts.ChartDataLabelPosition;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.*;
import com.spire.presentation.drawing.FillFormatType;
import com.spire.presentation.drawing.PatternFillType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class OperationPieChartTest {

    @Test
    public void testRead() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-饼图.pptx");
        IChart chart = prismaPptUtil.getChart("prisma-pie-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        //DataTable dataTable = chart.getChartDataTable();
        Iterator<ChartSeriesDataFormat> iterator = chart.getSeries().iterator();
        while (iterator.hasNext()) {
            ChartSeriesDataFormat chartSerie = iterator.next();
            System.out.println(chartSerie.getNamedRange().get(0).getText());
            int valuesCount = chartSerie.getValues().getCount();
            for (int i =0; i < valuesCount; i ++) {
                CellRange cellRange = chartSerie.getValues().get(i);
                System.out.println(cellRange.getText());
                cellRange.setNumberValue(Integer.valueOf((String) cellRange.getValue()) + 5);
            }
        }
        //保存文件
//        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-饼图.pptx");
    }

    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-饼图2.pptx");
        IChart chart = prismaPptUtil.getChart("prisma-pie-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("司龄", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("人数", DataTypes.DATATABLE_INT));

        DataRow row1 = dataTable.newRow();
        row1.setString("司龄", "1年以内（含1年）");
        row1.setInt("人数", 212);
        DataRow row2 = dataTable.newRow();
        row2.setString("司龄", "1-2年（含2年）");
        row2.setInt("人数", 134);
        DataRow row3 = dataTable.newRow();
        row3.setString("司龄", "2-5年（含5年");
        row3.setInt("人数", 152);
        DataRow row4 = dataTable.newRow();
        row4.setString("司龄", "5-10年（含10年）");
        row4.setInt("人数", 10);
        DataRow row5 = dataTable.newRow();
        row5.setString("司龄", "10年以上");
        row5.setInt("人数", 5);
        dataTable.getRows().add(row1);
        dataTable.getRows().add(row2);
        dataTable.getRows().add(row3);
        dataTable.getRows().add(row4);
        dataTable.getRows().add(row5);
        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "B1"));

        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A6"));

        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B6"));

        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(0).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
            dataLabel.setPercentageVisible(true);
            dataLabel.getTextFrame().setText("");
//            dataLabel.getTextFrame().getTextRange().getFill().setFillType(FillFormatType.SOLID);
//            dataLabel.getTextFrame().getTextRange().getFill().getSolidColor().setColor(Color.WHITE);
//            dataLabel.getTextProperties().getTextRange().getFill().setFillType(FillFormatType.SOLID);
//            dataLabel.getTextProperties().getTextRange().getFill().getSolidColor().setColor(Color.WHITE);
            dataLabel.getFill().setFillType(FillFormatType.SOLID);
            dataLabel.getFill().getSolidColor().setColor(Color.WHITE);
        }
        List<Color> pieColors = new ArrayList<>();
        pieColors.add(new Color(53 ,161, 153));
        pieColors.add(new Color(38 ,56 ,107));
        pieColors.add(new Color(71 ,182, 195));
        pieColors.add(new Color(26 ,30, 51));


        int colCount = dataTable.getColumns().size();
        for (int i = 0; i < chart.getSeries().get(0).getValues().getCount(); i++)
        {
            ChartDataPoint cdp = new ChartDataPoint(chart.getSeries().get(0));
            cdp.setIndex(i);
            chart.getSeries().get(0).getDataPoints().add(cdp);
        }
        for (int i = 0; i < chart.getSeries().get(0).getDataPoints().getCount(); i++)
        {
            System.out.println(i%pieColors.size());
//            chart.getSeries().get(0).getDataPoints().get(i).getFill().setFillType(FillFormatType.SOLID);
            chart.getSeries().get(0).getDataPoints().get(i).getFill().getSolidColor().setColor(pieColors.get(i%pieColors.size()));

        }

        System.out.println(chart.getSeries().get(0).getDataPoints().getCount());
        for (int i = 0; i < colCount - 1; i ++) {
//            chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
            System.out.println(i%pieColors.size());
//            chart.getCategories().get(i).getFill().getSolidColor().setColor(pieColors.get(i%pieColors.size()));
//
//
//            chart.Series[0].DataPoints[0].Fill.FillType = FillFormatType.Solid;
//            chart.Series[0].DataPoints[0].Fill.SolidColor.Color = Color.RosyBrown;
        }
        int size = chart.getChartLegend().getLegendEntrys().getCount();
        for (int i = 0; i < size; i ++) {
            chart.getChartLegend().getLegendEntrys().get(i).getTextProperties().setWordWrap(false);
        }
        chart.getChartLegend().setHeight(chart.getHeight());
        chart.getChartTitle().getTextProperties().setText("司龄");

        chart.getSeries().get(0).getFill().setFillType(FillFormatType.PATTERN);
        chart.getSeries().get(0).getFill().getPattern().setPatternType(PatternFillType.NARROW_HORIZONTAL);
        chart.getSeries().get(0).getFill().getPattern().getForegroundColor().setColor(Color.ORANGE);

        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-饼图2.pptx");
    }

}
