package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.FileFormat;
import com.spire.presentation.Presentation;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;


public class PptConvertToPdfTest {
    @Test
    public void test() throws Exception {
        Presentation presentation = new Presentation();
        presentation.loadFromFile(FileUtil.getInputFilePath() + "ppt-0505.pptx");
        presentation.saveToFile(FileUtil.getOutputFilePath() + "ppt-0505.pdf", FileFormat.PDF);
        System.out.println("--");
    }
}
