package pers.xdrodger.simple.test.ppt.poi;

import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFSlide;

import java.io.FileInputStream;
import java.io.FileOutputStream;

public class ReaderPpt {
    public static void main(String[] args) throws Exception {
        // slide name has been set via VBA ...
        FileInputStream fis = new FileInputStream("D:\\workspace\\practice-demo\\file\\input\\8 敬业度+3.0-报告样本.pptx");
        XMLSlideShow ppt = new XMLSlideShow(fis);
        fis.close();
        XSLFSlide sl = ppt.getSlides().get(0);
        System.out.println(sl.getXmlObject().getCSld().getName());
        // set slide name via POI and validate it
        sl.getXmlObject().getCSld().setName("new name");
        FileOutputStream fos = new FileOutputStream("D:\\workspace\\practice-demo\\file\\output\\8 敬业度+3.0-报告样本2.pptx");
        ppt.write(fos);
        fos.close();
        ppt.close();
        fis = new FileInputStream("D:\\workspace\\practice-demo\\file\\output\\8 敬业度+3.0-报告样本2.pptx");
        ppt = new XMLSlideShow(fis);
        fis.close();
        System.out.println(sl.getXmlObject().getCSld().getName());
        ppt.close();

    }
}
