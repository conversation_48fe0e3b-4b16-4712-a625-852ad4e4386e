package pers.xdrodger.simple.test.ppt.poi.vo;

import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class PptTableData {
    private String shapeName;
    private List<?> headList = Collections.EMPTY_LIST;
    private boolean isNeedMergeHead = false;
    private List<PptTableRowData> bodyList = Collections.EMPTY_LIST;
    private int pageSplitSize = 0;

    public PptTableData() {

    }

    public PptTableData(String shapeName, List<?> headList, List<PptTableRowData> rowDataList) {
        this.shapeName = shapeName;
        this.headList = headList;
        this.bodyList = rowDataList;
    }

    public PptTableData(String shapeName, List<?> headList, List<PptTableRowData> rowDataList, int pageSplitSize) {
        this.shapeName = shapeName;
        this.headList = headList;
        this.bodyList = rowDataList;
        this.pageSplitSize = pageSplitSize;
    }

    public PptTableData(String shapeName, List<?> headList, boolean isNeedMergeHead, List<PptTableRowData> rowDataList, int pageSplitSize) {
        this.shapeName = shapeName;
        this.headList = headList;
        this.isNeedMergeHead = isNeedMergeHead;
        this.bodyList = rowDataList;
        this.pageSplitSize = pageSplitSize;
    }
}
