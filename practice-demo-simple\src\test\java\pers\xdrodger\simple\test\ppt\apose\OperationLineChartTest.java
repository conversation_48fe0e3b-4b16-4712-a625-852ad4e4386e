package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.charts.ChartMarkerType;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;
import java.util.Iterator;

public class OperationLineChartTest {

    @Test
    public void testRead() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-折线图.pptx");
        IChart chart = prismaPptUtil.getChart("p5-demographic-history-line-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
//            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        //DataTable dataTable = chart.getChartDataTable();
        Iterator<ChartSeriesDataFormat> iterator = chart.getSeries().iterator();
        while (iterator.hasNext()) {
            ChartSeriesDataFormat chartSerie = iterator.next();
            System.out.println(chartSerie.getType());
            System.out.println(chartSerie.getFill().getSolidColor().getColor());
            System.out.println(chartSerie.getNamedRange().get(0).getText());
            System.out.println(chartSerie.getLine().getSolidFillColor().getColor());
            int valuesCount = chartSerie.getValues().getCount();
            for (int i =0; i < valuesCount; i ++) {
                CellRange cellRange = chartSerie.getValues().get(i);
                System.out.println(cellRange.getText());
//                cellRange.setNumberValue(Integer.valueOf((String) cellRange.getValue()) + 5);
            }
        }
    }

    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-折线图.pptx");
        IChart chart = prismaPptUtil.getChart("p5-demographic-history-line-chart");
//        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
//        while (categoryIterator.hasNext()) {
//            ChartCategory category = categoryIterator.next();
//            System.out.println(category.getDataRange().getText());
//            category.getDataRange().setText(category.getDataRange().getText() + 2);
//        }
        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("年份", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("敬业度指数2", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("满意度指数2", DataTypes.DATATABLE_DOUBLE));

        DataRow row1 = dataTable.newRow();
        row1.setString("年份", "2020年");
        row1.setDouble("敬业度指数2", 0.55);
        row1.setDouble("满意度指数2", 0.42);
        DataRow row2 = dataTable.newRow();
        row2.setString("年份", "2021年");
        row2.setDouble("敬业度指数2", 0.6);
        row2.setDouble("满意度指数2", 0.71);
        DataRow row3 = dataTable.newRow();
        row3.setString("年份", "2022年");
        row3.setDouble("敬业度指数2", 0.33);
        row3.setDouble("满意度指数2", 0.56);
        dataTable.getRows().add(row1);
        dataTable.getRows().add(row2);
        dataTable.getRows().add(row3);

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "C1"));

        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A4"));

        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B1", "B4"));
//        chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
//        chart.getSeries().get(0).getFill().getSolidColor().setColor(new Color(112, 173, 71));
        chart.getSeries().get(0).setType(ChartType.LINE_MARKERS);
        chart.getSeries().get(0).getLine().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getLine().getSolidFillColor().setColor(new Color(112, 173, 71));
        chart.getSeries().get(0).getMarkerFill().getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getMarkerFill().getFill().getSolidColor().setColor(new Color(112, 173, 71));
        chart.getSeries().get(0).setMarkerStyle(ChartMarkerType.CIRCLE);
        chart.getSeries().get(0).getMarkerFill().getLine().getFillFormat().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getMarkerFill().getLine().getFillFormat().getSolidFillColor().setColor(new Color(112, 173, 71));

        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C4"));
        chart.getSeries().get(1).setType(ChartType.LINE_MARKERS);
        chart.getSeries().get(1).getLine().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(1).getLine().getSolidFillColor().setColor(new Color(68, 114, 196));
        chart.getSeries().get(1).getMarkerFill().getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(1).getMarkerFill().getFill().getSolidColor().setColor(new Color(68, 114, 196));
        chart.getSeries().get(1).setMarkerStyle(ChartMarkerType.CIRCLE);
        chart.getSeries().get(0).getMarkerFill().getLine().getFillFormat().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getMarkerFill().getLine().getFillFormat().getSolidFillColor().setColor(new Color(68, 114, 196));

        chart.getChartTitle().getTextProperties().setText("男2");
        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-折线图.pptx");
    }

}
