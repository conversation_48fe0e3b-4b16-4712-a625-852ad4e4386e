package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.drawing.FillFormatType;
import com.spire.presentation.drawing.PatternFillType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;
import java.util.Iterator;

public class OperationPieColorChartTest {


    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-饼图颜色.pptx");
        IChart chart = prismaPptUtil.getChart("p1-preface-valid-answer-rate-pie-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
//        chart.getSeries().get(0).getDataPoints().get(0).getFill().getSolidColor().setColor(new Color(255, 255, 255));
        chart.getSeries().get(0).getDataPoints().get(0).getFill().setFillType(FillFormatType.PATTERN);
        chart.getSeries().get(0).getDataPoints().get(0).getFill().getPattern().setPatternType(PatternFillType.NARROW_VERTICAL);
        chart.getSeries().get(0).getDataPoints().get(0).getFill().getPattern().getForegroundColor().setColor(new Color(218, 227, 243));
//        chart.getSeries().get(0).getDataPoints().get(0).getFill().getPattern().getForegroundColor().setColor(new Color(255, 255, 255));
//        chart.getSeries().get(0).getDataPoints().get(0).getFill().getPattern().getBackgroundColor().setColor(new Color(255, 255, 255));

//        chart.getSeries().get(0).getDataPoints().get(0).getLine().setFillType(FillFormatType.SOLID);
//        chart.getSeries().get(0).getDataPoints().get(0).getLine().getFillFormat().setFillType(FillFormatType.SOLID);
//        chart.getSeries().get(0).getDataPoints().get(0).getLine().getSolidFillColor().setColor(Color.WHITE);
        chart.getSeries().get(0).getDataPoints().get(1).getFill().setFillType(FillFormatType.NONE);
        chart.getSeries().get(0).getDataPoints().get(1).getLine().setWidth(1);
        chart.getSeries().get(0).getDataPoints().get(1).getLine().getSolidFillColor().setColor(new Color(192,192, 192));


//        chart.getSeries().get(0).getLine().setWidth(3d);
//        chart.getSeries().get(0).getLine().getSolidFillColor().setColor(new Color(192,192, 192));
//        chart.getSeries().get(0).getDataPoints().get(1).getFill().setFillType(FillFormatType.PATTERN);
//        chart.getSeries().get(0).getDataPoints().get(1).getFill().getPattern().setPatternType(PatternFillType.NARROW_VERTICAL);
//        chart.getSeries().get(0).getDataPoints().get(1).getFill().getPattern().getForegroundColor().setColor(new Color(218, 227, 243));
//        chart.getSeries().get(0).getDataPoints().get(1).getFill().getPattern().getBackgroundColor().setColor(new Color(255, 255, 255));
        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-饼图颜色.pptx");
    }

}
