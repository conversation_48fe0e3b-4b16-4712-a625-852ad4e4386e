package pers.xdrodger.simple.test.print;

import org.assertj.core.util.DateUtil;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

public class PrintSqlTest {

    private static String PAGE = "PAGE";
    private static String BUTTON = "BUTTON";

    private String productId = "1281133960410337282";

    private static final String sql = "INSERT INTO permission\n" +
            "\t(id, code, name, type, product_id, create_by, create_time, update_time, last_update_by, is_deleted, app_id) \n" +
            "VALUES \n" +
            "\t('%s', '%s', '%s', '%s', '%s', NULL, '%s', '%s', NULL, 'f', NULL);";

    private static String formatTime2Millisecond = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 格式化为 yyyy-MM-dd HH:mm:ss.SSS
     * @param date
     * @return
     */
    public static String formatTime2Millisecond(Date date){
        String format = new SimpleDateFormat(formatTime2Millisecond).format(date);
        return format;
    }

    private String getUUID() {
        return UUID.randomUUID().toString();
    }

    private String printPageSql(String id, String code, String name) {
        Date now = new Date();
        String time = formatTime2Millisecond(now);
        String newSql = String.format(sql, id, code, name, PAGE, productId, time, time);
        System.out.println(newSql);
        return newSql;
    }

    private String printButtonSql(String id, String code, String name) {
        Date now = new Date();
        String time = formatTime2Millisecond(now);
        String newSql = String.format(sql, id, code, name, BUTTON, productId, time, time);
        System.out.println(newSql);
        return newSql;
    }

    @Test
    public void printUUID() {
        for (int i = 0; i < 10; i ++) {
            System.out.println(getUUID());
        }
    }

    @Test
    public void test() {
        // 活动管理
        printPageSql("15ca0cf4-beb7-40c3-8265-de4cbf54f766", "SAG:TENANT:PROJECT_MGT:LIST", "活动管理-活动列表");

        printButtonSql("18c55cdc-0ffd-4f91-9991-582208e28123", "SAG:TENANT:PROJECT_MGT:CREATE", "活动管理-新建活动");
        printButtonSql("5ab9243a-f269-4c12-b661-337a7fa9b97a", "SAG:TENANT:PROJECT_MGT:UPDATE", "活动管理-修改活动");

//        printButtonSql("56b84b6c-c223-4a63-b817-2568645c0f3c", "SAG:TENANT:PROJECT_MGT:DELETE", "活动管理-删除活动");
//        printButtonSql("", "SAG:TENANT:PROJECT_MGT:DELETE", "活动管理-删除活动");

        printButtonSql("3b8d6291-ff2e-4fd9-a3ae-16fcb46b3d23", "SAG:TENANT:PROJECT_MGT:COPY_PROJECT", "活动管理-复制活动");
        printButtonSql("05105adb-e75e-4985-95fb-a62c065d4f0f", "SAG:TENANT:PROJECT_MGT:UPDATE_CONFIG", "活动管理-更新配置");


        printButtonSql("520066d6-710e-4512-ab55-8cb549bc2c5d", "SAG:TENANT:PROJECT_MGT:REPAIR_ALGORITHM", "活动管理-矫正算法");
        printButtonSql("c9ffaadb-5d49-45bd-a738-75d0fe2bc7e4", "SAG:TENANT:PROJECT_MGT:RELATIONSHIP", "活动管理-评价关系");
        printButtonSql("8b4652b5-7d6f-440f-b8af-f5eabb404757", "SAG:TENANT:PROJECT_MGT:EDIT_QUESTION_BOOK", "活动管理-编辑题本");
        printButtonSql("ddea3793-5e8e-4b51-8b06-814b58d14a44", "SAG:TENANT:PROJECT_MGT:REPORT_CONFIG", "活动管理-报告配置表");
        printButtonSql("edbbaf3e-7065-48f1-9cc2-c6c0baa85963", "SAG:TENANT:PROJECT_MGT:REPORT_TYPE", "活动管理-报告类型");

        printPageSql("4707353e-898d-4992-9b72-35b16051e7ac", "SAG:TENANT:REPORT_MGT:REPORT_LIST", "报告管理-报告列表");
        printButtonSql("4576d472-8a88-4d72-af1c-19d8ce2d263e", "SAG:TENANT:REPORT_MGT:CREATE_GROUP_REPORT", "报告管理-创建团队报告");
        printButtonSql("9ac46d94-172e-4dae-b1b2-287af79fea38", "SAG:TENANT:REPORT_MGT:CREATE_COMPARE_REPORT", "报告管理-创建对比报告");
        printButtonSql("0e000d94-dc4d-4c04-89d0-356eb6a6c00a", "SAG:TENANT:REPORT_MGT:DOWNLOAD_REPORT", "报告管理-下载报告");

        printPageSql("1e741879-f59a-4f4c-b01a-24fc674019ee", "SAG:TENANT:HOME:HOME", "首页-首页");


//
//
//
//
//
//
//
//
//
//

//        printPageSql("SAG:TENANT:PROJECT_MGT:COPY_PROJECT", "复制活动");
    }
}
