package pers.xdrodger.simple.test.ppt.poi.practice;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.spire.presentation.*;
import com.spire.presentation.collections.ColumnCollection;
import com.spire.presentation.collections.ParagraphCollection;
import com.spire.presentation.collections.TableRowCollection;
import com.spire.presentation.drawing.IImageData;
import com.spire.presentation.packages.sprjzb;
import com.spire.xls.core.IChart;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.apose.SpirePrismaPptUtil;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableCellData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableRowData;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReplaceFrontChart.java
 * @Description 替换封面
 * @createTime 2024年05月24日 18:02:00
 */
public class CrossAnaChartReplace {

    @Test
    public void CrossAnaChartReplace() throws Exception {
/*        FileInputStream inputStream = new FileInputStream("D:\\data\\file\\print\\input\\test_input3.pptx");
        Presentation  presentation = new Presentation();
        presentation.loadFromStream(inputStream, FileFormat.PPTX_2013);

        List<IShape> shapes = getShapes(presentation,"p0-preface-cross-analysis-group-description-table");
        System.out.println(shapes.size());

        if(shapes.size() == 0){
            return;
        }

        ITable iTable = (ITable) shapes.get(0);
        System.out.println(iTable.getAlternativeText());

        TableRowCollection columnCollection = iTable.getTableRows();
        TableRow tableRow = columnCollection.get(0);
        ParagraphCollection paragraphCollection = tableRow.get(1).getTextFrame().getParagraphs();
        System.out.println(paragraphCollection.size());
        for(int i=0;i<paragraphCollection.size();i++){
            System.out.println(paragraphCollection.get(i).getText());
        }


        presentation.saveToFile("D:\\data\\file\\print\\output\\test_input3_"+System.currentTimeMillis()+".pptx",FileFormat.PPTX_2013);
        presentation.dispose();*/

        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil("D:\\data\\file\\print\\input\\test_input3.pptx");
        Presentation ppt = prismaPptUtil.getPpt();

        String shapeName ="p0-preface-cross-analysis-group-description-table";

        List<IShape> shapes = getShapes(ppt,shapeName);
        System.out.println(shapes.size());

        if(shapes.size() == 0){
            return;
        }

        /*ITable iTable = (ITable) shapes.get(0);
        System.out.println(iTable.getAlternativeText());

        TableRowCollection columnCollection = iTable.getTableRows();
        TableRow tableRow = columnCollection.get(0);
        ParagraphCollection paragraphCollection = tableRow.get(1).getTextFrame().getParagraphs();
        System.out.println(paragraphCollection.size());
        for(int i=0;i<paragraphCollection.size();i++){
            System.out.println(paragraphCollection.get(i).getText());
        }*/

        //表格数据
        List<PptTableRowData> bodyList = Lists.newArrayList();

        //标题行
        String titleText ="本次分析样本范围为 {{123213}}{{，1232131}}\n";
        /*PptTableRowData firstRowData = new PptTableRowData();
        bodyList.add(firstRowData);
        firstRowData.getCellDataList().add(PptTableCellData.builder().value(titleText).build());
        firstRowData.getCellDataList().add(PptTableCellData.builder().value(titleText).build());*/

        //表头行
       /* PptTableRowData secondRowData = new PptTableRowData();
        bodyList.add(secondRowData);
        secondRowData.getCellDataList().add(PptTableCellData.builder().value("标签类别").build());
        secondRowData.getCellDataList().add(PptTableCellData.builder().value("标签内容").build());*/
        List<String> headList = new ArrayList<>();
        headList.add("");
        headList.add(titleText);

        PptTableRowData secondRowData = new PptTableRowData();
        bodyList.add(secondRowData);
        secondRowData.getCellDataList().add(PptTableCellData.builder().value("标签类别").build());
        secondRowData.getCellDataList().add(PptTableCellData.builder().value("标签内容").build());
        for (int i = 0; i < 20; i++) {
            PptTableRowData rowData = new PptTableRowData();
            bodyList.add(rowData);
            rowData.getCellDataList().add(PptTableCellData.builder().value(String.valueOf(i)).build());
            rowData.getCellDataList().add(PptTableCellData.builder().value(String.valueOf(i)).build());
        }

        PptTableData pptTableData = new PptTableData();
        pptTableData.setShapeName(shapeName);
        pptTableData.setHeadList(headList);
        pptTableData.setBodyList(bodyList);
        prismaPptUtil.replaceTable(pptTableData);

        ppt.saveToFile("D:\\data\\file\\print\\output\\test_input3_"+System.currentTimeMillis()+".pptx",FileFormat.PPTX_2013);
        ppt.dispose();
    }

    public List<IShape> getShapes(Presentation ppt, String shapeName) {
        List<IShape> shapes = new ArrayList<>();
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                shapes.add(shape);
            }
        }
        return shapes;
    }

    public IShape getShape(ISlide slide, String shapeName) {
        ShapeCollection shapeCollection = slide.getShapes();
        Iterator<IShape> iterator = shapeCollection.iterator();
        while (iterator.hasNext()) {
            IShape shape = iterator.next();
            // 组合图形
            if (shape instanceof GroupShape) {
                IShape innerShape = getShapeFromGroupShape((GroupShape) shape, shapeName);
                if (innerShape != null) {
                    return innerShape;
                }
            }
            if (shape.getName().equals(shapeName)) {
                return shape;
            }
        }
        return null;
    }

    private IShape getShapeFromGroupShape(GroupShape groupShape, String shapeName) {
        Iterator<IShape> iterator = groupShape.getShapes().iterator();
        while (iterator.hasNext()) {
            IShape innerShape = iterator.next();
            if (innerShape.getName().equals(shapeName)) {
                return innerShape;
            }
            // 内部图形还是租户图形，递归处理
            if (innerShape instanceof GroupShape) {
                IShape shape = getShapeFromGroupShape((GroupShape) innerShape, shapeName);
                if (shape != null) {
                    return shape;
                }
            }
        }
        return null;
    }
}
