# practice-demo

## 项目介绍

这是一个Java工具类项目，包含各种实用工具类。

## 工具类说明

### ExcelDataValidator

Excel数据校验工具类，用于校验Excel文件中特定位置的数据一致性。

主要功能：
- 从指定行和列开始，按对校验数据一致性（例如第4行与第5行比较，第6行与第7行比较，而第5行与第6行不比较）
- 支持.xls和.xlsx格式的Excel文件
- 提供详细的错误信息报告
- 支持校验指定名称开头的工作表

使用方法：
```java
ExcelDataValidator validator = new ExcelDataValidator();
ExcelDataValidator.ValidationResult result = validator.validateData("path/to/your/file.xlsx");

if (result.isValid()) {
    System.out.println("数据校验通过");
} else {
    System.out.println("数据校验失败，错误信息：");
    for (String error : result.getErrors()) {
        System.out.println(error);
    }
}
```

默认从第3行第6列开始校验，也可以指定起始行和列：
```java
// 从第5行第3列开始校验
ExcelDataValidator.ValidationResult result = validator.validateData("path/to/your/file.xlsx", 4, 2);
```

校验以指定名称开头的工作表：
```java
// 校验以"数据汇总表"开头的工作表，从第3行第6列开始
ExcelDataValidator.ValidationResult result = validator.validateDataBySheetNamePrefix("path/to/your/file.xlsx", "数据汇总表", 3, 5);
```

### 其他工具类

- ToolUtil: 包含各种通用工具方法
- DateUtil: 日期处理工具类
- RadixTrans: 进制转换工具类
- SnowflakeKeyGenerator: 雪花算法ID生成器
- ...