package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.KnownColors;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;
import java.util.Iterator;

public class OperationBarChartTest {

    @Test
    public void testRead() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-柱状图.pptx");
        IChart chart = prismaPptUtil.getChart("department-analysis-department-eei-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
//            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        //DataTable dataTable = chart.getChartDataTable();
        Iterator<ChartSeriesDataFormat> iterator = chart.getSeries().iterator();
        while (iterator.hasNext()) {
            ChartSeriesDataFormat chartSerie = iterator.next();
            System.out.println(chartSerie.getNamedRange().get(0).getText());
            int valuesCount = chartSerie.getValues().getCount();
            for (int i =0; i < valuesCount; i ++) {
                CellRange cellRange = chartSerie.getValues().get(i);
                System.out.println(cellRange.getText());
//                cellRange.setNumberValue(Integer.valueOf((String) cellRange.getValue()) + 5);
            }
        }
        //保存文件
//        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-饼图.pptx");
    }

    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-柱状图2.pptx");
        IChart chart = prismaPptUtil.getChart("department-analysis-department-eei-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("部门", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("敬业度指数", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("公司", DataTypes.DATATABLE_DOUBLE));
        dataTable.getColumns().add(new DataColumn("全行业均分", DataTypes.DATATABLE_DOUBLE));

        DataRow row1 = dataTable.newRow();
        row1.setString("部门", "射手座");
        row1.setDouble("敬业度指数", 0.86);
        row1.setDouble("公司", 0.1);
        row1.setDouble("全行业均分", 0.8);
        DataRow row2 = dataTable.newRow();
        row2.setString("部门", "调研");
        row2.setDouble("敬业度指数", 0.6);
        row2.setDouble("公司", 0.1);
        row2.setDouble("全行业均分", 0.7);
        DataRow row3 = dataTable.newRow();
        row3.setString("部门", "售后");
        row3.setDouble("敬业度指数", 0.89);
        row3.setDouble("公司", 0.3);
        row3.setDouble("全行业均分", 0.6);
        dataTable.getRows().add(row1);
        dataTable.getRows().add(row2);
        dataTable.getRows().add(row3);

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "D1"));

        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A4"));

        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B4"));
        chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(0).getFill().getSolidColor().setColor(Color.BLUE);


        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C4"));
//        chart.getSeries().get(1).getFill().getSolidColor().setKnownColor(KnownColors.LIGHT_GRAY);
        chart.getSeries().get(1).setType(ChartType.LINE_MARKERS);
        //将系列2绘制在次坐标轴
//        chart.getSeries().get(1).setUseSecondAxis(true);
        chart.getSeries().get(2).setValues(chart.getChartData().get("D2", "D4"));
        chart.getSeries().get(2).setType(ChartType.LINE_MARKERS);

//        for (int i =0; i < dataTable.getRows().size(); i ++) {
//            ChartDataLabel dataLabel = chart.getSeries().get(0).getDataLabels().add();
//            //显示标签的值
//            dataLabel.setLabelValueVisible(true);
//            dataLabel.setPercentageVisible(true);
//        }

        chart.getChartTitle().getTextProperties().setText("司龄");
//        chart.getPrimaryCategoryAxis().setMinValue(0);
//        chart.getPrimaryCategoryAxis().setMaxValue(1);
//        chart.getPrimaryValueAxis().setMinValue(0);
//        chart.getPrimaryValueAxis().setMaxValue(0);
        chart.getPrimaryValueAxis().isVisible(true);
        chart.getPrimaryValueAxis().isAutoMajor(false);
        chart.getPrimaryValueAxis().isAutoMin(false);
        chart.getPrimaryValueAxis().setMinValue(0);
        chart.getPrimaryValueAxis().setMaxValue(1);
        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-柱状图2.pptx");
    }

}
