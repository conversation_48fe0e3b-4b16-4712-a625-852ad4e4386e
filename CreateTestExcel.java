import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;

public class CreateTestExcel {
    
    public static void main(String[] args) {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        
        // 创建工作表
        Sheet sheet = workbook.createSheet("测试数据");
        
        // 创建行和单元格并填充数据
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("列1");
        headerRow.createCell(1).setCellValue("列2");
        headerRow.createCell(2).setCellValue("列3");
        headerRow.createCell(3).setCellValue("列4");
        headerRow.createCell(4).setCellValue("列5");
        headerRow.createCell(5).setCellValue("校验列");
        
        // 创建数据行
        // 第1行数据
        Row dataRow1 = sheet.createRow(1);
        dataRow1.createCell(5).setCellValue("数据A");
        
        // 第2行数据（与第1行相同）
        Row dataRow2 = sheet.createRow(2);
        dataRow2.createCell(5).setCellValue("数据A");
        
        // 第3行数据（与第2行不同）
        Row dataRow3 = sheet.createRow(3);
        dataRow3.createCell(5).setCellValue("数据B");
        
        // 第4行数据（与第3行相同）
        Row dataRow4 = sheet.createRow(4);
        dataRow4.createCell(5).setCellValue("数据B");
        
        // 第5行数据（与第4行不同）
        Row dataRow5 = sheet.createRow(5);
        dataRow5.createCell(5).setCellValue("数据C");
        
        // 保存文件
        try (FileOutputStream outputStream = new FileOutputStream("test-data.xlsx")) {
            workbook.write(outputStream);
            workbook.close();
            System.out.println("测试Excel文件创建成功！");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}