package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.IShape;
import com.spire.presentation.Presentation;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartDataLabel;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;
import java.util.Iterator;

public class AdjustPiePieChartTest {
    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-适配饼图.pptx");
        Presentation ppt = prismaPptUtil.getPpt();
        IChart chart = prismaPptUtil.getChart("prisma-pie-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("司龄", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("人数", DataTypes.DATATABLE_INT));

        DataRow row1 = dataTable.newRow();
        row1.setString("司龄", "1年以内（含1年）");
        row1.setInt("人数", 212);
        DataRow row2 = dataTable.newRow();
        row2.setString("司龄", "1-2年（含2年）");
        row2.setInt("人数", 134);
        DataRow row3 = dataTable.newRow();
        row3.setString("司龄", "2-5年（含5年");
        row3.setInt("人数", 152);
        DataRow row4 = dataTable.newRow();
        row4.setString("司龄", "5-10年（含10年）名字很长很长很长很长很长很长很长很长很长很长很长很长");
        row4.setInt("人数", 10);
        DataRow row5 = dataTable.newRow();
        row5.setString("司龄", "10年以上");
        row5.setInt("人数", 5);
        DataRow row6 = dataTable.newRow();
        row6.setString("司龄", "20年以上");
        row6.setInt("人数", 5);
        DataRow row7 = dataTable.newRow();
        row7.setString("司龄", "30年以上很长很长很长很长很长很长很长很长很长很长很长很长很长很长");
        row7.setInt("人数", 5);
        DataRow row8 = dataTable.newRow();
        row8.setString("司龄", "40年以上");
        row8.setInt("人数", 5);
        DataRow row9 = dataTable.newRow();
        row9.setString("司龄", "50年以上");
        row9.setInt("人数", 5);
        DataRow row10 = dataTable.newRow();
        row10.setString("司龄", "60年以上");
        row10.setInt("人数", 5);
        DataRow row11 = dataTable.newRow();
        row11.setString("司龄", "70年以上");
        row11.setInt("人数", 5);
        DataRow row12 = dataTable.newRow();
        row12.setString("司龄", "80年以上");
        row12.setInt("人数", 5);
        DataRow row13 = dataTable.newRow();
        row13.setString("司龄", "90年以上");
        row13.setInt("人数", 5);
        DataRow row14 = dataTable.newRow();
        row14.setString("司龄", "100年以上");
        row14.setInt("人数", 5);
        DataRow row15 = dataTable.newRow();
        row15.setString("司龄", "110年以上");
        row15.setInt("人数", 5);
        DataRow row16 = dataTable.newRow();
        row16.setString("司龄", "120年以上");
        row16.setInt("人数", 5);
        DataRow row17 = dataTable.newRow();
        row17.setString("司龄", "130年以上");
        row17.setInt("人数", 5);
        DataRow row18 = dataTable.newRow();
        row18.setString("司龄", "140年以上");
        row18.setInt("人数", 5);
        DataRow row19 = dataTable.newRow();
        row19.setString("司龄", "150年以上");
        row19.setInt("人数", 5);
        DataRow row20 = dataTable.newRow();
        row20.setString("司龄", "160年以上");
        row20.setInt("人数", 5);
        dataTable.getRows().add(row1);
        dataTable.getRows().add(row2);
        dataTable.getRows().add(row3);
        dataTable.getRows().add(row4);
        dataTable.getRows().add(row5);
        dataTable.getRows().add(row6);
        dataTable.getRows().add(row7);
        dataTable.getRows().add(row8);
        dataTable.getRows().add(row9);
        dataTable.getRows().add(row10);
        dataTable.getRows().add(row11);
        dataTable.getRows().add(row12);
        dataTable.getRows().add(row13);
        dataTable.getRows().add(row14);
        dataTable.getRows().add(row15);
        dataTable.getRows().add(row16);
        dataTable.getRows().add(row17);
        dataTable.getRows().add(row18);
        dataTable.getRows().add(row19);
        dataTable.getRows().add(row20);
        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "B1"));
        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A22"));
        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B22"));
        for (int i =0; i < dataTable.getRows().size(); i ++) {
            ChartDataLabel dataLabel = chart.getSeries().get(0).getDataLabels().add();
            //显示标签的值
            dataLabel.setLabelValueVisible(true);
            dataLabel.setPercentageVisible(true);
            dataLabel.getTextFrame().setText("");
            dataLabel.getFill().setFillType(FillFormatType.SOLID);
            dataLabel.getFill().getSolidColor().setColor(Color.WHITE);
        }
        System.out.println(chart.getChartLegend().getLegendEntrys().getCount());
        int size = chart.getChartLegend().getLegendEntrys().getCount();
        for (int i = 0; i < size; i ++) {
            chart.getChartLegend().getLegendEntrys().get(i).getTextProperties().setWordWrap(false);
        }
        System.out.println(chart.getHeight());
        chart.getChartLegend().setHeight(chart.getHeight());
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-适配饼图-output.pptx");
    }
}
