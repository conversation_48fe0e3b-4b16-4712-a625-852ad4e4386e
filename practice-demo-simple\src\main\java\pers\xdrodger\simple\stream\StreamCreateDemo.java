package pers.xdrodger.simple.stream;

import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Title: StreamCreateDemo
 * @ProjectName practice-demo
 * @Description:
 * @date 5/13/21 11:12 PM
 */
public class StreamCreateDemo {

    @Test
    public void createByCollection() {
        List<String> list = new ArrayList<>();
        Stream<String> stream = list.stream();
        Stream<String> parallelStream = list.parallelStream();
    }

    @Test
    public void createByArray() {
        int[] array = {1, 2, 3, 4, 5};
        IntStream stream = Arrays.stream(array);
    }

    @Test
    public void createByStaticMethod() {
        Stream<Integer> stream = Stream.of(1, 2, 3, 4, 5);
        Stream<Integer> stream2 = Stream.iterate(0, (x) -> x + 3).limit(3); // 输出 0,3,6
        Stream<String> stream3 = Stream.generate(() -> "Hello").limit(3); // 输出 Hello,Hello,Hello
        Stream<Double> stream4 = Stream.generate(Math::random).limit(3); // 输出3个随机数
    }

    @Test
    public void demo4() {
// 生成有限的常量流
        IntStream intStream = IntStream.range(1, 3); // 输出 1,2
        IntStream intStream2 = IntStream.rangeClosed(1, 3); // 输出 1,2,3
// 生成一个等差数列
        IntStream.iterate(1, i -> i + 3).limit(5).forEach(System.out::println); // 输出 1,4,7,10,13
// 生成无限常量数据流
        IntStream generate = IntStream.generate(() -> 10).limit(3); // 输出 10,10,10
    }
}
