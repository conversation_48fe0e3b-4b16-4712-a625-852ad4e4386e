package pers.xdrodger.simple.test.ppt.apose;

import com.spire.ms.System.Collections.IEnumerator;
import com.spire.presentation.*;
import com.spire.xls.core.ITextBoxShape;
import javafx.scene.control.TreeTableRow;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.util.Iterator;

public class ReadText {


    @Test
    public void testReadText() throws Exception {
        //实例化一个Presentation对象
        SpirePrismaPptUtil pptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-组合图形.pptx");
        IShape shape = pptUtil.getShape("p0-front-cover-year-text");
        System.out.println(shape.getName());
        System.out.println(shape instanceof IAutoShape);
        System.out.println(shape instanceof ITextBoxShape);

    }

    public static void main(String[] args) throws Exception {




    }

}