package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataTable;
import com.spire.ms.System.Collections.IEnumerator;
import com.spire.presentation.*;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.xls.charts.ChartSerie;
import com.spire.xls.core.IChartCategoryAxis;
import com.spire.xls.core.IChartSerie;
import com.spire.xls.core.IXLSRange;
import javafx.scene.control.TreeTableRow;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.time.temporal.ChronoField;
import java.util.Iterator;
import com.spire.presentation.charts.*;

public class ReadChart {

    private IChart getChart(ISlide slide, String tableName) {
        ShapeCollection shapeCollection = slide.getShapes();
        IEnumerator it = shapeCollection.iterator();
        while (it.hasNext()) {
            IShape shape = (IShape) it.next();
            System.out.println(shape.getName());
            if (shape.getName().equals(tableName)) {
                return (IChart) shape;
            }
        }
        return null;
    }

    @Test
    public void testReadChart() throws Exception {
        //实例化一个Presentation对象
        Presentation presentation = new Presentation();

        presentation.loadFromFile(FileUtil.getInputFilePath() + "demo-赞成度表.pptx");
        ISlide slide = presentation.getSlides().get(0);
        IChart chart = getChart(slide, "eei-new-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
        }
        //DataTable dataTable = chart.getChartDataTable();
        Iterator<ChartSeriesDataFormat> iterator = chart.getSeries().iterator();
        while (iterator.hasNext()) {
            ChartSeriesDataFormat chartSerie = iterator.next();
            System.out.println(chartSerie.getNamedRange().get(0).getText());
            Iterator<CellRange> iterator1 = chartSerie.getValues().iterator();
            while (iterator1.hasNext()) {
                CellRange cellRange = iterator1.next();
                System.out.println(cellRange.getText());
                cellRange.setNumberValue(5);
            }

        }

        //保存文件
        presentation.saveToFile(FileUtil.getOutputFilePath() + "demo-赞成度表.pptx", FileFormat.PPTX_2013);
    }

    public static void main(String[] args) throws Exception {




    }

}