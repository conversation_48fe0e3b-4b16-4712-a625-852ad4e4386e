package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;

public class OperationCreateTableTest {


    @Test
    public void testReplace() throws Exception {
        //实例化一个Presentation对象
        Presentation presentation = new Presentation();

        //设置表格行数和列数、行高和列宽
        Double[] widths = new Double[] { 80d, 80d, 100d, 80d };
        Double[] heights = new Double[] { 15d, 15d, 15d, 15d, 15d, 15d };

        //添加一个表格
        ITable table = presentation.getSlides().get(0).getShapes().appendTable((float)presentation.getSlideSize().getSize().getWidth() / 2 - 275, 90, widths, heights);

        //设置表格内置样式
        table.setStylePreset(TableStylePreset.LIGHT_STYLE_3_ACCENT_1);

        //设置对齐方式
        for (int i = 0; i < 4; i++)
        {
            table.get(i, 0).getTextFrame().getParagraphs().get(0).setAlignment(TextAlignmentType.CENTER);
        }

        //声明一个String数组
        String[][] dataStr = new String[][]
                {
                        {"姓名", "性别", "部门", "工号"},
                        {"Winny", "女", "综合", "0109"},
                        {"Lois", "女", "综合", "0111"},
                        {"Jois", "男", "技术", "0110"},
                        {"Moon", "女", "销售", "0112"},
                        {"Winny", "女", "后勤", "0113"}
                };

        //向表格中填充数据
        for (int i = 0; i < 6; i++)
        {
            for (int j = 0; j < 4; j++)
            {
                table.get(j, i).getTextFrame().setText(dataStr[i][j]);
                table.get(j, i).getBorderBottom().setWidth(3);
                table.get(j, i).getBorderBottom().getFillFormat().setFillType(FillFormatType.SOLID);
                table.get(j, i).getBorderBottom().getSolidFillColor().setColor(Color.WHITE);
            }
        }

        //保存文件
        presentation.saveToFile(FileUtil.getOutputFilePath() + "demo-创建表格.pptx", FileFormat.PPTX_2013);

    }
}