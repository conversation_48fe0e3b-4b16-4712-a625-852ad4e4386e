package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.*;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.drawing.FillFormatType;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.util.ArrayList;
import java.util.List;

public class PPTTest {
    public static void main(String[] args) throws Exception {
        testReplaceZhCN();
    }
    private static List<List<Object>> getZhCNDataList() {
        List<List<Object>> result = new ArrayList<>();
        List<Object> item1 = new ArrayList<>();
        item1.add("好方向");
        item1.add(64.3);
        item1.add(64.3);
        item1.add(64);
        result.add(item1);
        List<Object> item2 = new ArrayList<>();
        item2.add("好文化");
        item2.add(76);
        item2.add(76);
        item2.add(77.8);
        result.add(item2);
        List<Object> item3 = new ArrayList<>();
        item3.add("好员工");
        item3.add(62.5);
        item3.add(62.5);
        item3.add(63.1);
        result.add(item3);
        List<Object> item4 = new ArrayList<>();
        item4.add("好领导");
        item4.add(81.3);
        item4.add(81.3);
        item4.add(82.9);
        result.add(item4);
        List<Object> item5 = new ArrayList<>();
        item5.add("好工作");
        item5.add(76.5);
        item5.add(67.5);
        item5.add(71.4);
        result.add(item5);
        return result;
    }

    public static void testReplaceZhCN() throws Exception {
        Presentation prismaPptUtil = new Presentation();
        prismaPptUtil.loadFromFile(FileUtil.getInputFilePath() + "demo-雷达图-0329.pptx");
        ISlide slide=prismaPptUtil.getSlides().get(0);
        for (int s = 0; s < slide.getShapes().getCount(); s++) {
            IShape shape=slide.getShapes().get(s);
            if (shape instanceof IChart)
            {
                IChart chart = (IChart) shape;
                DataTable dataTable = new DataTable();
                String dimensionName = "维度名称";
                String dimensionScore = "维度得分";
                String company = "公司整体";
                String norm = "外部常模";
                dataTable.getColumns().add(new DataColumn(dimensionName, DataTypes.DATATABLE_STRING));
                dataTable.getColumns().add(new DataColumn(dimensionScore, DataTypes.DATATABLE_DOUBLE));
                dataTable.getColumns().add(new DataColumn(company, DataTypes.DATATABLE_DOUBLE));
                dataTable.getColumns().add(new DataColumn(norm, DataTypes.DATATABLE_DOUBLE));
                List<List<Object>> dataList = getZhCNDataList();
                for (int i = 0; i < dataList.size(); i ++) {
                    List<Object> data = dataList.get(i);
                    DataRow row = dataTable.newRow();
                    for (int j = 0; j < data.size(); j ++) {
                        if (j == 0) {
                            row.setString(dimensionName, (String) data.get(j));
                        }
                        if (j == 1) {
                            row.setDouble(dimensionScore, Double.parseDouble(data.get(j).toString()));
                        }
                        if (j == 2) {
                            row.setDouble(company, Double.parseDouble(data.get(j).toString()));
                        }
                        if (j == 3) {
                            row.setDouble(norm, Double.parseDouble(data.get(j).toString()));
                        }
                    }
                    dataTable.getRows().add(row);
                }

                //将数据写入图表
                for (int c = 0; c < dataTable.getColumns().size(); c++) {
                    chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
                }
                for (int r = 0; r < dataTable.getRows().size(); r++) {
                    Object[] datas = dataTable.getRows().get(r).getArrayList();
                    for (int c = 0; c < datas.length; c++) {
                        chart.getChartData().get(r + 1, c).setValue(datas[c]);

                    }
                }
                //设置类别标签
                chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A6"));
                //设置系列标签
                chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "D1"));
                //为各个系列赋值
                chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B6"));
                chart.getSeries().get(0).setType(ChartType.RADAR_FILLED);
                chart.getSeries().get(0).getFill().setFillType(FillFormatType.SOLID);
                chart.getSeries().get(0).getFill().getSolidColor().setKnownColor(KnownColors.BLUE);

                chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C6"));
                // 使用次坐标轴
                //chart.getSeries().get(1).setUseSecondAxis(true);
                chart.getSeries().get(1).setType(ChartType.RADAR);
                //将系列2绘制在次坐标轴
                chart.getSeries().get(2).setValues(chart.getChartData().get("D2", "D6"));
//                chart.getSeries().get(2).setUseSecondAxis(true);
                chart.getSeries().get(2).setType(ChartType.RADAR);

            }
        }
        //保存文件
        prismaPptUtil.saveToFile(FileUtil.getOutputFilePath() + "demo-雷达图-0420-zhCN.pptx",FileFormat.PPTX_2013);
    }

}
