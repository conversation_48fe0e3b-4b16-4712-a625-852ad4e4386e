package pers.xdrodger.simple.test.ppt.poi.vo;

import com.spire.presentation.charts.ChartType;
import lombok.Data;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;

import java.awt.*;
import java.util.List;

@Data
public class ChartCategorySeriesData {
    private String name;
    private int column;
    private String dataRangeReference;
    private String titleRangeReference;
    private String formatCode;
    private List<?> dataList;
    private XDDFDataSource valueData;
    private ChartType chartType;
    private Color color;
}
