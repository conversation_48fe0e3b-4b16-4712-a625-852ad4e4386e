package pers.xdrodger.simple.test.prisma;

import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.apache.lucene.util.RamUsageEstimator;
import org.junit.Test;
import pers.xdrodger.util.SnowflakeKeyGenerator;

import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public class PrismaBTest {

    @Test
    public void testExcelDemo() {
        PearsonsCorrelation pearsonsCorrelation = new PearsonsCorrelation();
        double[] xArray = new double[]{
                4, 1, 2, 6, 6,
                3, 6, 1, 5, 4
        };
        double[] yArray = new double[]{
                6, 5, 1, 1, 3,
                6, 5, 5, 2, 2
        };
        double r = pearsonsCorrelation.correlation(xArray, yArray);
        System.out.println(r);
    }

    @Test
    public void testExcelDemo2() {
        PearsonsCorrelation pearsonsCorrelation = new PearsonsCorrelation();
        double[] xArray = new double[]{
                4,
                6,
                6,
                1,
                6,
                2,
                2,
                4,
                2,
                2,
                3,
                6,
                6,
                4,
                3,
                4,
                3,
                1,
                6,
                6,
                3,
                6,
                6,
                6,
                6,
                2,
                6,
                2,
                5,
                1,
                4,
                1,
                1,
                2,
                1,
                2,
                2

        };
        double[] yArray = new double[]{
                3,
                1,
                5,
                6,
                5,
                3,
                4,
                4,
                4,
                1,
                5,
                4,
                4,
                2,
                6,
                6,
                1,
                3,
                1,
                1,
                1,
                4,
                5,
                3,
                1,
                4,
                5,
                2,
                1,
                5,
                2,
                1,
                1,
                5,
                3,
                4,
                2

        };
        System.out.println(Arrays.toString(xArray));
        System.out.println(Arrays.toString(yArray));
        double r = pearsonsCorrelation.correlation(xArray, yArray);
        System.out.println(r);
    }


    @Test
    public void testExcelDemo3() {
        PearsonsCorrelation pearsonsCorrelation = new PearsonsCorrelation();
        double[] xArray = new double[]{
                4,
                6,
                6,
                1,
                6,
                2,
                2,
                4,
                2,
                2,
                3,
                6,
                6,
                4,
                3,
                4,
                3,
                1,
                6,
                6,
                3,
                6,
                6,
                6,
                6,
                2,
                6,
                2,
                5,
                1,
                4,
                1,
                1,
                2,
                1,
                2,
                2

        };
        double[] yArray = new double[]{
                3,
                1,
                5,
                6,
                5,
                3,
                4,
                4,
                4,
                1,
                5,
                4,
                4,
                2,
                6,
                6,
                1,
                3,
                1,
                1,
                1,
                4,
                5,
                3,
                1,
                4,
                5,
                2,
                1,
                5,
                2,
                1,
                1,
                5,
                3,
                4,
                2

        };
        List<Double> xArrayList = new ArrayList<>();
        for (int i = 0; i < xArray.length; i ++) {
            xArrayList.add(xArray[i]);
        }
        List<Double> yArrayList = new ArrayList<>();
        for (int i = 0; i < yArray.length; i ++) {
            yArrayList.add(yArray[i]);
        }
        Collections.shuffle(xArrayList);
        Collections.shuffle(yArrayList);

        double[] xArray2 = new double[xArrayList.size()];
        double[] yArray2 = new double[yArrayList.size()];
        for (int i = 0; i < xArrayList.size(); i ++) {
            xArray2[i] = xArrayList.get(i);
        }
        for (int i = 0; i < yArrayList.size(); i ++) {
            yArray2[i] = yArrayList.get(i);
        }
        System.out.println(Arrays.toString(xArray2));
        System.out.println(Arrays.toString(yArray2));
        double r = pearsonsCorrelation.correlation(xArray2, yArray2);
        System.out.println(r);
    }



    @Test
    public void test50000Sample() {
        PearsonsCorrelation pearsonsCorrelation = new PearsonsCorrelation();
        int eeiSize = 6;
        int otherSize = 69;
        int sampleSize = 50000;
        int[] score = new int[]{
                1, 2, 3, 4, 5, 6
        };
        long startTIme = System.currentTimeMillis();
        for (int k = 0; k < eeiSize * otherSize; k++) {
            double[] xArray = new double[sampleSize];
            Random random = new Random();
            for (int i = 0; i < sampleSize; i++) {
                xArray[i] = score[random.nextInt(6)];
            }
//            System.out.println(JSON.toJSONString(xArray));
            double[] yArray = new double[sampleSize];
            for (int i = 0; i < sampleSize; i++) {
                yArray[i] = score[random.nextInt(6)];
            }
            double r = pearsonsCorrelation.correlation(xArray, yArray);
            System.out.println(r);
        }
        long endTime = System.currentTimeMillis();
        System.out.println("cost time=" + (endTime - startTIme));
    }

    @Test
    public void testEEI() {
        int subOrgSize = 20;
        int analysisFactor = 8;
        int analysisFactorOption = 20;
        int sampleSize = 50000;
        int eeiSize = 6;
        int otherSize = 69;
        int[] score = new int[]{
                1, 2, 3, 4, 5, 6
        };
        long startTIme = System.currentTimeMillis();
        Random random = new Random();
        for (int i = 0; i < (eeiSize + otherSize); i++) {
            for (int j = 0; j < 9; j++) {
                for (int k = 0; k < 20; k++) {
                    int xSize = sampleSize / 20;
                    int[] xArray = new int[xSize];
                    int approveSize = 0;
                    for (int m = 0; m < xSize; m++) {
                        xArray[m] = score[random.nextInt(6)];
                        if (xArray[m] == 6 || xArray[m] == 5) {
                            approveSize++;
                        }
                    }
                    System.out.println(String.format("第%d题，第%d个分析因子，第%d个选项值，有效填答人数=%d，赞成人数=%d, 赞成度=%.2f", i + 1, j + 1, k + 1, xSize, approveSize, (approveSize / (xSize + 0.0) * 100)));
                }
            }
        }
        long endTime = System.currentTimeMillis();
        System.out.println("cost time=" + (endTime - startTIme));
    }

    @Test
    public void testLargeData() {
        int sampleSize = 50000;
        int questionSize = 75;
        int organizationSize = 200;
        int analysisFactorSize = 8;
        int analysisFactorOptionSize = 20;
        int[] score = new int[]{
                1, 2, 3, 4, 5, 6
        };
        long startTIme = System.currentTimeMillis();
        Random random = new Random();
        SnowflakeKeyGenerator worker = new SnowflakeKeyGenerator(1,1,1);
        List<String> questionList = new ArrayList<>(questionSize);
        for (int i = 0; i < questionSize; i ++) {
            questionList.add(worker.nextId() + "");
        }
        List<String> organizationList = new ArrayList<>(organizationSize);
        for (int i = 0; i < organizationSize; i ++) {
            organizationList.add(worker.nextId() + "");
        }
        List<List<String>> analysisFactorGroup = new ArrayList<>();
        for (int i=0; i < analysisFactorSize; i ++) {
            List<String> analysisFactor = new ArrayList<>();
            for (int j = 0; j < analysisFactorOptionSize; j ++) {
                analysisFactor.add(worker.nextId() + "");
            }
            analysisFactorGroup.add(analysisFactor);
        }
        List<String> personList = new ArrayList<>();
        for (int i =0; i < sampleSize; i ++) {
            personList.add(worker.nextId() + "");
        }
        // 组装分析因子数据
        List<EltPersonDemographic> personDemographicList = new ArrayList<>(sampleSize);
        for (int i =0; i < sampleSize; i ++) {
            EltPersonDemographic demographic = new EltPersonDemographic();
            demographic.setPersonId(personList.get(i));
            demographic.setOrganizationId(organizationList.get(random.nextInt(200)));
            demographic.setC1(analysisFactorGroup.get(0).get(random.nextInt(20)));
            demographic.setC2(analysisFactorGroup.get(1).get(random.nextInt(20)));
            demographic.setC3(analysisFactorGroup.get(2).get(random.nextInt(20)));
            demographic.setC4(analysisFactorGroup.get(3).get(random.nextInt(20)));
            demographic.setC5(analysisFactorGroup.get(4).get(random.nextInt(20)));
            demographic.setC6(analysisFactorGroup.get(5).get(random.nextInt(20)));
            demographic.setC7(analysisFactorGroup.get(6).get(random.nextInt(20)));
            demographic.setC8(analysisFactorGroup.get(7).get(random.nextInt(20)));
            personDemographicList.add(demographic);
        }

        List<EltPersonAnswer> personAnswerList = new ArrayList<>();
        for (int i =0; i < sampleSize; i ++) {
            String personId = personDemographicList.get(i).getPersonId();
            String organizationId = personDemographicList.get(i).getOrganizationId();
            for (int j =0; j < questionSize; j ++) {
                EltPersonAnswer personAnswer = new EltPersonAnswer();
                personAnswer.setPersonId(personId);
                personAnswer.setOrganizationId(organizationId);
                personAnswer.setQuestionId(questionList.get(j));
                personAnswer.setScore(score[random.nextInt(6)]);
                personAnswerList.add(personAnswer);
            }
        }
//        System.out.println(JSON.toJSONString(personDemographicList));
//        System.out.println(JSON.toJSONString(personAnswerList));
        long endTime = System.currentTimeMillis();
        System.out.println("size of personDemographicList=" + RamUsageEstimator.humanSizeOf(personDemographicList));
        System.out.println("size of personAnswerList=" + RamUsageEstimator.humanSizeOf(personAnswerList));
        System.out.println("cost time=" + (endTime - startTIme));
        String c1_v1 = analysisFactorGroup.get(0).get(0);
        List<String> c1Condition = new ArrayList<>();
        c1Condition.add(analysisFactorGroup.get(0).get(0));
        c1Condition.add(analysisFactorGroup.get(0).get(1));
        List<String> c2Condition = new ArrayList<>();
        c2Condition.add(analysisFactorGroup.get(1).get(0));
        c2Condition.add(analysisFactorGroup.get(1).get(1));
        personDemographicList.stream().filter(n -> c1Condition.contains(n.getC1())).filter(n -> c2Condition.contains(n.getC2()));
        personDemographicList.stream().filter(n ->
                c1Condition.contains(n.getC1()) && c2Condition.contains(n.getC2())
        );
    }

    @Test
    public void testFilter() {
        int sampleSize = 15000;
        int questionSize = 75;
        Random random = new Random();
        SnowflakeKeyGenerator worker = new SnowflakeKeyGenerator(1,1,1);
        List<String> optionIdList = new ArrayList<>(6);
        optionIdList.add("1");
        optionIdList.add("2");
        optionIdList.add("3");
        optionIdList.add("4");
        optionIdList.add("5");
        optionIdList.add("6");
        List<String> questionIdList = new ArrayList<>(questionSize);
        for (int i = 0; i < questionSize; i ++) {
            questionIdList.add(worker.nextId() + "");
        }
        List<String> personIdList = new ArrayList<>();
        for (int i =0; i < sampleSize; i ++) {
            personIdList.add(worker.nextId() + "");
        }
        List<SurveyAnswer> answers = new ArrayList<>();
        for (int i =0; i < sampleSize; i ++) {
            for (int j=0; j < questionSize; j ++) {
                SurveyAnswer answer = new SurveyAnswer();
                answer.setPersonId(personIdList.get(i));
                answer.setQuestionId(questionIdList.get(j));
                answer.setOptionId(optionIdList.get(random.nextInt(6)));
                answers.add(answer);
            }
        }
        String questionId =  questionIdList.get(random.nextInt(questionSize));
        List<String> validPersonIds = personIdList;
        Map<String, List<SurveyAnswer>> answersMap = answers.stream().collect(Collectors.groupingBy(SurveyAnswer::getQuestionId));
        List<SurveyAnswer> questionAnswers = answersMap.get(questionId);
        long startTIme = System.currentTimeMillis();
        for (int i =0; i < 10; i ++) {
            List<SurveyAnswer> list = answers.parallelStream().filter(n -> n.getQuestionId().equals(questionId)).filter(n -> validPersonIds.contains(n.getPersonId())).collect(Collectors.toList());
//            List<SurveyAnswer> list = questionAnswers.parallelStream().filter(n -> validPersonIds.contains(n.getPersonId())).collect(Collectors.toList());
        }
        long endTIme = System.currentTimeMillis();
        System.out.println("cost=" + (endTIme-startTIme));
        //List<SurveyAnswer> list = answers.stream().filter(n -> n.getQuestionId().equals(questionId)).filter(n -> validPersonIds.contains(n.getPersonId())).collect(Collectors.toList());
//        long startTIme2 = System.currentTimeMillis();
//        for (int i =0; i < questionSize * 2; i ++) {
//            List<SurveyAnswer> list = answersMap.get(questionId).parallelStream().filter(n -> validPersonIds.contains(n.getPersonId())).collect(Collectors.toList());
//        }
//        long endTIme2 = System.currentTimeMillis();
//        System.out.println("cost=" + (endTIme2-startTIme2));

        //long startTIme3 = System.currentTimeMillis();
        //List<SurveyAnswer> questionAnswers = answersMap.get(questionId);
        //for (int i =0; i < questionSize * 2; i ++) {
        //    List<SurveyAnswer> list = new ArrayList<>();
        //    for (SurveyAnswer answer : questionAnswers) {
        //        if (validPersonIds.contains(answer.getPersonId())) {
        //            list.add(answer);
        //        }
        //    }
        //}
        //long endTIme3 = System.currentTimeMillis();
        //System.out.println("cost=" + (endTIme3-startTIme3));


    }

}
