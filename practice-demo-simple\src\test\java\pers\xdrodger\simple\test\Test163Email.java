package pers.xdrodger.simple.test;

import lombok.Data;
import pers.xdrodger.util.DateUtil;
import pers.xdrodger.util.ReportHumanInterpretationEnum;
import pers.xdrodger.util.StandardReportTypeEnum;

import java.util.Date;
import java.util.Properties;
import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

public class Test163Email {
    // 发件人 账号和密码
    public static final String MY_EMAIL_ACCOUNT = "<EMAIL>";
    public static final String MY_EMAIL_PASSWORD = "XKBUCZGSBVSDQODQ";// 密码,是你自己的设置的授权码

    // SMTP服务器(这里用的163 SMTP服务器)
    public static final String MEAIL_163_SMTP_HOST = "smtp.163.com";
    public static final String SMTP_163_PORT = "25";// 端口号,这个是163使用到的;QQ的应该是465或者875

    // 收件人
    //public static final String RECEIVE_EMAIL_ACCOUNT = "<EMAIL>";
    public static final String RECEIVE_EMAIL_ACCOUNT = "<EMAIL>";

    public static void main(String[] args) throws AddressException, MessagingException {
        Properties p = new Properties();
        p.setProperty("mail.smtp.host", MEAIL_163_SMTP_HOST);
        p.setProperty("mail.smtp.port", SMTP_163_PORT);
        p.setProperty("mail.smtp.socketFactory.port", SMTP_163_PORT);
        p.setProperty("mail.smtp.auth", "true");
        p.setProperty("mail.smtp.socketFactory.class", "SSL_FACTORY");

        Session session = Session.getInstance(p, new Authenticator() {
            // 设置认证账户信息
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(MY_EMAIL_ACCOUNT, MY_EMAIL_PASSWORD);
            }
        });
        session.setDebug(true);
        System.out.println("创建邮件");
        MimeMessage message = new MimeMessage(session);
        // 发件人
        message.setFrom(new InternetAddress(MY_EMAIL_ACCOUNT));
        // 收件人和抄送人
        message.setRecipients(Message.RecipientType.TO, RECEIVE_EMAIL_ACCOUNT);
//		message.setRecipients(Message.RecipientType.CC, MY_EMAIL_ACCOUNT);

        // 内容(这个内容还不能乱写,有可能会被SMTP拒绝掉;多试几次吧)
        message.setSubject("测评邀请");
        //message.setContent("您好：\n" +
        //        "诚挚邀请您参与本次测评活动。\n" +
        //        "请您在{{开始日期}}和{{结束日期}}之间登录{{链接}}进行填答<font color=\"#FF0000\">（建议使用Google Chrome浏览器填答）</font>。\n" +
        //        "请您预留好充足的时间，一次性完成测评活动，谢谢。", "text/html;charset=UTF-8");

        TenantVO tenantVO = new TenantVO();
        tenantVO.setName("sag");
        tenantVO.setLoginName("sag_admin");

        SurveyStandardReportInterpretation reportInterpretation = new SurveyStandardReportInterpretation();
        reportInterpretation.setUserName("徐冬冬");
        reportInterpretation.setTelNum("***********");
        reportInterpretation.setReportType(StandardReportTypeEnum.AMA);
        reportInterpretation.setReportHumanInterpretation(ReportHumanInterpretationEnum.TEL);
        reportInterpretation.setCreateTime(new Date());
        String content = getContetnt(tenantVO, reportInterpretation);
        message.setContent(content, "text/html;charset=UTF-8");

        message.setSentDate(new Date());
        message.saveChanges();
        System.out.println("准备发送");
        Transport.send(message);
    }

    private static String getContetnt(TenantVO tenantVO,SurveyStandardReportInterpretation surveyStandardReportInterpretation){
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("您有新的订单待处理，具体信息如下：");
        stringBuilder.append("<table border='2' bordercolor='black'>");
        stringBuilder.append("<tr><td colspan ='2' style='text-align:center'>客户信息</td></tr>");
        stringBuilder.append("<tr><td>公司名称</td><td>");
        stringBuilder.append(tenantVO.getName());
        stringBuilder.append("</td></tr>");
        stringBuilder.append("<tr><td>公司登陆名</td><td>");
        stringBuilder.append(tenantVO.getLoginName());
        stringBuilder.append("</td></tr>");
        stringBuilder.append("<tr><td>联系人姓名</td><td>");
        stringBuilder.append(surveyStandardReportInterpretation.getUserName());
        stringBuilder.append("</td></tr>");
        stringBuilder.append("<tr><td>手机号</td><td>");
        stringBuilder.append(surveyStandardReportInterpretation.getTelNum());
        stringBuilder.append("</td></tr>");
        stringBuilder.append("<tr><td colspan ='2' style='text-align:center'>订单详情</td></tr>");
        stringBuilder.append("<tr><td>解读报告的类型</td><td>");
        stringBuilder.append(surveyStandardReportInterpretation.getReportType().getName());
        stringBuilder.append("</td></tr>");
        stringBuilder.append("<tr><td>解读形式</td><td>");
        stringBuilder.append(surveyStandardReportInterpretation.getReportHumanInterpretation().getName());
        stringBuilder.append("</td></tr>");
        stringBuilder.append("<tr><td>下单时间</td><td>");
        stringBuilder.append(DateUtil.formatNormTime(surveyStandardReportInterpretation.getCreateTime()));
        stringBuilder.append("</td></tr>");
        stringBuilder.append("</table>");
        return stringBuilder.toString();
    }

    @Data
    public static class TenantVO {
        private String name;
        private String loginName;
    }

    @Data
    public static class SurveyStandardReportInterpretation {
        private String userName;
        private String telNum;
        private StandardReportTypeEnum reportType;
        private ReportHumanInterpretationEnum reportHumanInterpretation;
        private Date createTime;
    }
}