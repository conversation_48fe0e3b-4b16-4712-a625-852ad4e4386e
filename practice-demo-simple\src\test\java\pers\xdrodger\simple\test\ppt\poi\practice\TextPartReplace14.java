package pers.xdrodger.simple.test.ppt.poi.practice;

import com.google.common.collect.Lists;
import com.spire.presentation.*;
import com.spire.presentation.collections.ColumnCollection;
import org.junit.Test;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReplaceFrontChart.java
 * @Description 替换封面
 * @createTime 2024年05月24日 18:02:00
 */
public class TextPartReplace14 {

    @Test
    public void textPartReplace() throws Exception {
        FileInputStream inputStream = new FileInputStream("D:\\data\\file\\print\\input\\test_input14.pptx");
        Presentation presentation = new Presentation();
        presentation.loadFromStream(inputStream, FileFormat.PPTX_2013);

        List<IShape> shapes = getShapes(presentation, "p4-directory-name-text");
        IAutoShape mainTitleShape = (IAutoShape) shapes.get(0);

        List<IShape> shapes2 = getShapes(presentation, "p4-directory-child-name-text");
        IAutoShape subTitlesShape = (IAutoShape) shapes2.get(0);

        System.out.println(mainTitleShape.getTextFrame().getTextRange().getText());

        String[][] datas = {{"数据附录  ","分析主体题目数据"},{"行动方案  ","参考表单"},{"213213  ","1232311"}};

        ParagraphEx defaultParagraphEx = subTitlesShape.getTextFrame().getParagraphs().get(0);
        for (int i = 0; i < datas.length; i++) {
            ParagraphEx paragraphEx = new ParagraphEx();

            PortionEx portionEx1 = new PortionEx();
            portionEx1.setText(datas[i][0]);
            portionEx1.getFormat().isBold(TriState.TRUE);
            portionEx1.getFormat().setLatinFont(new TextFont("微软雅黑 (正文)"));
            portionEx1.setFontHeight(18);
            paragraphEx.getTextRanges().append(portionEx1);

            PortionEx portionEx2 = new PortionEx();
            portionEx2.setText(datas[i][1]+"\n");
            portionEx2.getFormat().isBold(TriState.FALSE);
            portionEx2.getFormat().setLatinFont(new TextFont("微软雅黑 (正文)\n"));
            portionEx2.setFontHeight(14);
            paragraphEx.getTextRanges().append(portionEx2);

            paragraphEx.setBulletType(defaultParagraphEx.getBulletType());
            paragraphEx.setBulletStyle(defaultParagraphEx.getBulletStyle());
            paragraphEx.setBulletNumber(defaultParagraphEx.getBulletNumber());
            paragraphEx.setBulletChar(defaultParagraphEx.getBulletChar());
            paragraphEx.setBulletFont(defaultParagraphEx.getBulletFont());
            paragraphEx.setBulletSize(defaultParagraphEx.getBulletSize());
            subTitlesShape.getTextFrame().getParagraphs().append(paragraphEx);
        }



        presentation.saveToFile("D:\\data\\file\\print\\output\\test_input14_"+System.currentTimeMillis()+".pptx",FileFormat.PPTX_2013);
        presentation.dispose();
    }

    public List<IShape> getShapes(Presentation ppt, String shapeName) {
        List<IShape> shapes = new ArrayList<>();
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                shapes.add(shape);
            }
        }
        return shapes;
    }

    public IShape getShape(ISlide slide, String shapeName) {
        ShapeCollection shapeCollection = slide.getShapes();
        Iterator<IShape> iterator = shapeCollection.iterator();
        while (iterator.hasNext()) {
            IShape shape = iterator.next();
            // 组合图形
            if (shape instanceof GroupShape) {
                IShape innerShape = getShapeFromGroupShape((GroupShape) shape, shapeName);
                if (innerShape != null) {
                    return innerShape;
                }
            }
            if (shape.getName().equals(shapeName)) {
                return shape;
            }
        }
        return null;
    }

    private IShape getShapeFromGroupShape(GroupShape groupShape, String shapeName) {
        Iterator<IShape> iterator = groupShape.getShapes().iterator();
        while (iterator.hasNext()) {
            IShape innerShape = iterator.next();
            if (innerShape.getName().equals(shapeName)) {
                return innerShape;
            }
            // 内部图形还是租户图形，递归处理
            if (innerShape instanceof GroupShape) {
                IShape shape = getShapeFromGroupShape((GroupShape) innerShape, shapeName);
                if (shape != null) {
                    return shape;
                }
            }
        }
        return null;
    }
}
