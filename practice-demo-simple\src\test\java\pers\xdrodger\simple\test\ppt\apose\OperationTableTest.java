package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import com.spire.presentation.drawing.FillFormatType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableCellData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptTableRowData;

import java.awt.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.List;

public class OperationTableTest {

    @Test
    public void testRead() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-赞成度表.pptx");
        ITable table = prismaPptUtil.getTable("eei-new-table");
        int rowCount = table.getTableRows().getCount();
        int colCount = table.getColumnsList().getCount();
        for (int i = 0; i < rowCount; i ++) {
            for (int j =0; j < colCount; j ++) {
                Cell cell = table.get(j, i);
                System.out.println(cell.getTextFrame().getText());
            }
        }
    }

    private Double getRandomDouble() {
        Random random = new Random();
        return new BigDecimal(random.nextDouble() * 100).setScale(1, BigDecimal.ROUND_UP).doubleValue();
    }


    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-赞成度表2.pptx");
        List<String> headList = new ArrayList<>();
        headList.add(null);
        headList.add(null);
        headList.add("分析主体2");
        headList.add("VS公司整体2");
        headList.add("VS20202");
        headList.add("VS20212");
        headList.add("VS2023");
        headList.add("VS2024");
        headList.add("VS2025");
        headList.add("VS2026");

        List<PptTableRowData> rowDataList = new LinkedList<>();
        Random random = new Random();
        random.nextDouble();
        for (int  i = 0; i < 10; i ++) {
            PptTableRowData rowData = new PptTableRowData();
            List<PptTableCellData> cellDataList = new ArrayList<>();
            cellDataList.add(new PptTableCellData("子维度" + i));
            cellDataList.add(new PptTableCellData(""));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            cellDataList.add(new PptTableCellData(getRandomDouble()));
            rowData.setCellDataList(cellDataList);
            rowDataList.add(rowData);
        }
        PptTableData pptTableData = new PptTableData();
        pptTableData.setShapeName("eei-new-table");
        pptTableData.setHeadList(headList);
        pptTableData.setBodyList(rowDataList);


        ITable table = prismaPptUtil.getTable("eei-new-table");
        int rowCount = table.getTableRows().getCount();
        int colCount = table.getColumnsList().getCount();
        if (colCount < headList.size()) {
            for (int i = colCount; i < headList.size(); i ++) {
                TableColumn column =table.getColumnsList().get(colCount-1);
                table.getColumnsList().add(column);
            }
        }
        if (rowCount < (1 + rowDataList.size())) {
            for (int i = rowCount; i < (1 + rowDataList.size()); i ++) {
                TableRow row = table.getTableRows().get(rowCount - 1);
                table.getTableRows().append(row);
            }
        }
        rowCount = table.getTableRows().getCount();
        colCount = table.getColumnsList().getCount();
        for (int i = 0; i < rowCount; i ++) {
            for (int j =0; j < colCount; j ++) {
                Cell cell = table.get(j, i);
                if (i == 0) {
                    String value = headList.get(j);
                    if (value == null) {
                        value = "";
                    }
                    cell.getTextFrame().setText(value);
                    cell.getTextFrame().getTextRange().getFill().setFillType(FillFormatType.SOLID);
                    cell.getTextFrame().getTextRange().getFill().getSolidColor().setColor(new Color(255, 0, 0));
                } else {
                     Object value = rowDataList.get(i -1).getCellDataList().get(j).getValue();
                     if (value == null) {
                         cell.getTextFrame().setText("");
                     } else if (value instanceof Double) {
                         cell.getTextFrame().setText(((Double) value).toString());
                     } else {
                         cell.getTextFrame().setText(((String) value));
                     }
                }

            }
        }

        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-赞成度表2.pptx");
    }
}