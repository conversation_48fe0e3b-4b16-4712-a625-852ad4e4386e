package pers.xdrodger.simple.test.ppt.poi.practice;

import com.itextpdf.text.Paragraph;
import com.spire.presentation.*;
import com.spire.presentation.collections.ParagraphCollection;
import com.spire.presentation.collections.TextRangeCollection;
import org.junit.Test;

import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReplaceFrontChart.java
 * @Description 替换封面
 * @createTime 2024年05月24日 18:02:00
 */
public class TextPartReplace2 {

    @Test
    public void textPartReplace() throws Exception {
        FileInputStream inputStream = new FileInputStream("D:\\data\\file\\print\\input\\test_input5.pptx");
        Presentation presentation = new Presentation();
        presentation.loadFromStream(inputStream, FileFormat.PPTX_2013);

        List<IShape> shapes = getShapes(presentation, "och-eei-driver-description-text");
        IAutoShape autoShape = (IAutoShape) shapes.get(0);
        ParagraphCollection paragraphCollection = autoShape.getTextFrame().getParagraphs();
        for (int i = 0; i < paragraphCollection.size(); i++) {
            ParagraphEx paragraphEx = paragraphCollection.get(i);
            String text = paragraphEx.getText();
            System.out.println(text);
            if (i == 1) {
                TextRangeCollection textRangeCollection = paragraphEx.getTextRanges();
                for (int j = 0; j < textRangeCollection.size(); j++) {
                    PortionEx portionEx = textRangeCollection.get(j);
                    String text2 = portionEx.getText();
                    text2 = text2.replace("{{", "");
                    text2 = text2.replace("}}", "");
                    if (text2.equals("自定义指数")) {
                        text2 = text2.replace("自定义指数", "123213");
                    }
                    portionEx.setText(text2);
                }

            }

        }
        paragraphCollection.removeAt(0);

        presentation.saveToFile("D:\\data\\file\\print\\output\\test_input5_"+System.currentTimeMillis()+".pptx",FileFormat.PPTX_2013);
        presentation.dispose();
    }

    public List<IShape> getShapes(Presentation ppt, String shapeName) {
        List<IShape> shapes = new ArrayList<>();
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                shapes.add(shape);
            }
        }
        return shapes;
    }

    public IShape getShape(ISlide slide, String shapeName) {
        ShapeCollection shapeCollection = slide.getShapes();
        Iterator<IShape> iterator = shapeCollection.iterator();
        while (iterator.hasNext()) {
            IShape shape = iterator.next();
            // 组合图形
            if (shape instanceof GroupShape) {
                IShape innerShape = getShapeFromGroupShape((GroupShape) shape, shapeName);
                if (innerShape != null) {
                    return innerShape;
                }
            }
            if (shape.getName().equals(shapeName)) {
                return shape;
            }
        }
        return null;
    }

    private IShape getShapeFromGroupShape(GroupShape groupShape, String shapeName) {
        Iterator<IShape> iterator = groupShape.getShapes().iterator();
        while (iterator.hasNext()) {
            IShape innerShape = iterator.next();
            if (innerShape.getName().equals(shapeName)) {
                return innerShape;
            }
            // 内部图形还是租户图形，递归处理
            if (innerShape instanceof GroupShape) {
                IShape shape = getShapeFromGroupShape((GroupShape) innerShape, shapeName);
                if (shape != null) {
                    return shape;
                }
            }
        }
        return null;
    }
}
