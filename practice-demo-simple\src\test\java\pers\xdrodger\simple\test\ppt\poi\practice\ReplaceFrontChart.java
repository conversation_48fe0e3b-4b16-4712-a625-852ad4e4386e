package pers.xdrodger.simple.test.ppt.poi.practice;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.spire.presentation.*;
import com.spire.presentation.drawing.IImageData;
import com.sun.deploy.net.HttpUtils;
import org.apache.poi.xslf.usermodel.*;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ReplaceFrontChart.java
 * @Description 替换封面
 * @createTime 2024年05月24日 18:02:00
 */
public class ReplaceFrontChart {

    @Test
    public void replaceFrontChart() throws Exception {
        FileInputStream inputStream = new FileInputStream("D:\\data\\file\\print\\input\\test_input.pptx");
        Presentation  presentation = new Presentation();
        presentation.loadFromStream(inputStream, FileFormat.PPTX_2013);

        List<IShape> shapes = getShapes(presentation,"bg_img");
        System.out.println(shapes.size());

        if(shapes.size() == 0){
            return;
        }

        HttpRequest request = HttpUtil.createGet("https://static.wildto.com/news/20236/1689695953299");
        HttpResponse response = request.execute();
        IImageData imageData = presentation.getImages().append(response.bodyStream());

        SlidePicture slidePicture = (SlidePicture) shapes.get(0);
        slidePicture.getPictureFill().getPicture().setEmbedImage(imageData);

        presentation.saveToFile("D:\\data\\file\\print\\output\\test_input"+System.currentTimeMillis()+".pptx",FileFormat.PPTX_2013);
        presentation.dispose();
    }

    public List<IShape> getShapes(Presentation ppt, String shapeName) {
        List<IShape> shapes = new ArrayList<>();
        Iterator<ISlide> iterator = ppt.getSlides().iterator();
        while (iterator.hasNext()) {
            ISlide slide = iterator.next();
            IShape shape = getShape(slide, shapeName);
            if (shape != null) {
                shapes.add(shape);
            }
        }
        return shapes;
    }

    public IShape getShape(ISlide slide, String shapeName) {
        ShapeCollection shapeCollection = slide.getShapes();
        Iterator<IShape> iterator = shapeCollection.iterator();
        while (iterator.hasNext()) {
            IShape shape = iterator.next();
            // 组合图形
            if (shape instanceof GroupShape) {
                IShape innerShape = getShapeFromGroupShape((GroupShape) shape, shapeName);
                if (innerShape != null) {
                    return innerShape;
                }
            }
            if (shape.getName().equals(shapeName)) {
                return shape;
            }
        }
        return null;
    }

    private IShape getShapeFromGroupShape(GroupShape groupShape, String shapeName) {
        Iterator<IShape> iterator = groupShape.getShapes().iterator();
        while (iterator.hasNext()) {
            IShape innerShape = iterator.next();
            if (innerShape.getName().equals(shapeName)) {
                return innerShape;
            }
            // 内部图形还是租户图形，递归处理
            if (innerShape instanceof GroupShape) {
                IShape shape = getShapeFromGroupShape((GroupShape) innerShape, shapeName);
                if (shape != null) {
                    return shape;
                }
            }
        }
        return null;
    }
}
