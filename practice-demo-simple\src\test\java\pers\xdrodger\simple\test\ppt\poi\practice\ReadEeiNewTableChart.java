package pers.xdrodger.simple.test.ppt.poi.practice;

import org.apache.poi.xslf.usermodel.XSLFChart;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTable;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.*;

import java.math.BigDecimal;
import java.util.*;


public class ReadEeiNewTableChart {

    private Double getRandomDouble() {
        Random random = new Random();
        return new BigDecimal(random.nextDouble() * 100).setScale(1, BigDecimal.ROUND_UP).doubleValue();
    }


    @Test
    public void updateEeiLineText() throws Exception {
        PrismaPptUtil prismaPptUtil = new PrismaPptUtil(FileUtil.getInputFilePath() + "demo-赞成度表.pptx");
        XSLFSlide slide = prismaPptUtil.getSlideByShapeName("eei-new-table");
        XSLFTable table = prismaPptUtil.getTable(slide,"eei-new-table");
        XSLFChart chart = prismaPptUtil.getChart(slide,"eei-new-chart");
        System.out.println("RowHeight= " + table.getRowHeight(0));
        System.out.println("ColumnWidth= " + table.getColumnWidth(0));
        System.out.println(chart.getOrAddManualLayout().getX());
        System.out.println(chart.getOrAddManualLayout().getY());
        System.out.println(chart.getOrAddManualLayout().getHeightRatio());
        System.out.println(chart.getOrAddManualLayout().getWidthRatio());
//        chart.getCTChart().getPlotArea().get
//        System.out.println(chart.getOrAddShapeProperties().getLineProperties().getWidth());
//        System.out.println(chart.getOrAddShapeProperties().getLineProperties().getWidth());
        System.out.println();

    }
}
