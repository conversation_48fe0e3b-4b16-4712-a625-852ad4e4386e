package pers.xdrodger.util;

import java.util.Stack;


public class RadixTrans {
    private static final char[] charSet = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_".toCharArray();

    /**
     * 10进制转62进制
     *
     * @param number
     * @return
     */
    public static String hex10To62(Long number) {
        Stack<Character> stack = new Stack<Character>();
        StringBuilder result = new StringBuilder(0);
        while (number != 0) {
            stack.add(charSet[new Long((number - (number / 62) * 62)).intValue()]);
            number = number / 62;
        }
        for (; !stack.isEmpty(); ) {
            result.append(stack.pop());
        }
        return result.toString();
    }

    /**
     * 62进制转10进制
     *
     * @param sourceStr
     * @return
     */
    public static String hex62To10(String sourceStr) {
        Long dst = 0L;
        for (int i = 0; i < sourceStr.length(); i++) {
            char c = sourceStr.charAt(i);
            for (int j = 0; j < charSet.length; j++) {
                if (c == charSet[j]) {
                    dst = (dst * 62) + j;
                    break;
                }
            }
        }
        return dst.toString();
    }


    public static void main(String[] args) {
        System.out.println(hex10To62(1354678068152762369L));
        System.out.println(hex62To10("1C4rh4gjTHj"));
    }


}