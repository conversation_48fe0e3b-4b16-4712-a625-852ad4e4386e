package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.*;
import com.spire.pdf.tables.table.*;
import com.spire.presentation.charts.*;
import com.spire.presentation.drawing.FillFormatType;
import java.awt.geom.Rectangle2D;
import java.lang.Object;


public class CreateChart {
    public static void main(String[] args) throws Exception {

        //实例化一个Presentation对象
        Presentation presentation = new Presentation();

        //插入柱形图

        Rectangle2D.Double rect = new Rectangle2D.Double(40, 100, 550, 320);
        IChart chart = null;
        chart = presentation.getSlides().get(0).getShapes().appendChart(ChartType.COLUMN_CLUSTERED, rect);

        //添加表名
        chart.getChartTitle().getTextProperties().setText("销售报表");
        chart.getChartTitle().getTextProperties().isCentered(true);
        chart.getChartTitle().setHeight(30);
        chart.hasTitle(true);

        //创建后台数据表
        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("销售额", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("谷物", DataTypes.DATATABLE_INT));
        dataTable.getColumns().add(new DataColumn("粮油", DataTypes.DATATABLE_INT));
        dataTable.getColumns().add(new DataColumn("百货", DataTypes.DATATABLE_INT));
        DataRow row1 = dataTable.newRow();
        row1.setString("销售额", "门店1");
        row1.setInt("谷物", 250);
        row1.setInt("粮油", 150);
        row1.setInt("百货", 99);
        DataRow row2 = dataTable.newRow();
        row2.setString("销售额", "门店2");
        row2.setInt("谷物", 270);
        row2.setInt("粮油", 150);
        row2.setInt("百货", 99);
        DataRow row3 = dataTable.newRow();
        row3.setString("销售额", "门店3");
        row3.setInt("谷物", 310);
        row3.setInt("粮油", 120);
        row3.setInt("百货", 49);
        DataRow row4 = dataTable.newRow();
        row4.setString("销售额", "门店4");
        row4.setInt("谷物", 330);
        row4.setInt("粮油", 120);
        row4.setInt("百货", 49);
        DataRow row5 = dataTable.newRow();
        row5.setString("销售额", "门店5");
        row5.setInt("谷物", 360);
        row5.setInt("粮油", 150);
        row5.setInt("百货", 141);
        DataRow row6 = dataTable.newRow();
        row6.setString("销售额", "门店6");
        row6.setInt("谷物", 380);
        row6.setInt("粮油", 150);
        row6.setInt("百货", 135);
        dataTable.getRows().add(row1);
        dataTable.getRows().add(row2);
        dataTable.getRows().add(row3);
        dataTable.getRows().add(row4);
        dataTable.getRows().add(row5);
        dataTable.getRows().add(row6);

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }

        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "D1"));

        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A7"));

        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B7"));
        chart.getSeries().get(1).setValues(chart.getChartData().get("C2", "C7"));
        chart.getSeries().get(2).setValues(chart.getChartData().get("D2", "D7"));
        chart.getSeries().get(2).getFill().setFillType(FillFormatType.SOLID);
        chart.getSeries().get(2).getFill().getSolidColor().setKnownColor(KnownColors.LIGHT_BLUE);

        //设置系列重叠
        chart.setOverLap(-50);

        //设置类别间距
        chart.setGapDepth(200);

        //保存文档
        presentation.saveToFile("D:\\workspace\\practice-demo\\file\\output\\CreateChart.pptx", FileFormat.PPTX_2013);

    }
}