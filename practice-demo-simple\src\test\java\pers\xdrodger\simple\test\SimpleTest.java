package pers.xdrodger.simple.test;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Test;
import pers.xdrodger.simple.Food;
import pers.xdrodger.simple.enums.FoodType;

import javax.sound.midi.Soundbank;
import java.io.Serializable;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class SimpleTest {

    private List<Food> getFoodList() {
        List<Food> result = new ArrayList<>();
        Random random = new Random();
        int length = random.nextInt( 10) + 10;
        for (int i = 0; i < length; i ++) {
            Food food = new Food();
            food.setId(UUID.randomUUID().toString());
            food.setName(RandomStringUtils.randomAlphabetic(5));
            if (i % 4 == 0) {
                food.setType(FoodType.Fruit);
            } else if (i % 4 == 1) {
                food.setType(FoodType.Vegetable);
            } else if (i % 4 == 2) {
                food.setType(FoodType.Meat);
            } else {
                food.setType(FoodType.Grain);
            }
            result.add(food);
        }
        return result;
    }

    @Test
    public void testFilter() {
        List<Food> result = getFoodList();
        System.out.println(JSON.toJSONString(result));

        List<Food>  fruitList = result.stream().filter(n -> n.getType().equals(FoodType.Fruit)).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(fruitList));
    }

    @Test
    public void testString() {
        String str = "ABc";
        System.out.println(str.toLowerCase());
    }

    @Test
    public void testMap() {
        Map<String, String> map = new HashMap<>();
        map.put("1", null);
        map.put("2", "");
        System.out.println(JSON.toJSONString(map));
        System.out.println(map.get("1"));
    }

    @Test
    public void testExtractString() {
//        String rawString = "<p>测试<span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">4554{{</span><span style=\\\"font-size: 13.5pt; color: #6a8759; font-family: 宋体;\\\">我</span><span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">:</span><span style=\\\"font-size: 13.5pt; color: #6a8759; font-family: 宋体;\\\">我</span><span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">,</span><span style=\\\"font-size: 13.5pt; color: #6a8759; font-family: 宋体;\\\">你</span><span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">,</span><span style=\\\"font-size: 13.5pt; color: #6a8759; font-family: 宋体;\\\">他</span><span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">}}52323{{</span><span style=\\\"font-size: 13.5pt; color: #6a8759; font-family: 宋体;\\\">你</span><span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">:</span><span style=\\\"font-size: 13.5pt; color: #6a8759; font-family: 宋体;\\\">我</span><span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">,</span><span style=\\\"font-size: 13.5pt; color: #6a8759; font-family: 宋体;\\\">你</span><span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">,</span><span style=\\\"font-size: 13.5pt; color: #6a8759; font-family: 宋体;\\\">他</span><span style=\\\"font-family: 'JetBrains Mono'; font-size: 13.5pt; color: #6a8759;\\\">}}</span></p>";
//        String rawString = "哈哈哈{{我:我,你,他}}，哈哈{{我:我,你,他组织}}哈看看";
        String rawString = "<p>哈哈{{我:我,你,他}}，哈哈{{我:我,你,他组织}}哈看看</p>\",\"zh_CN\":\"<p>哈哈<em>{{我:我,你,他}}</em>，哈哈<strong>{{我:我,你,他组织}}</strong>哈看看</p>";
        String express = "\\{\\{(.*?)[\\|\\|.*?]?}}";
        System.out.println(express);
        Matcher match = Pattern.compile(express).matcher(rawString);
        while (match.find()) {
            System.out.println(match.group(1));
        }
        System.out.println();
    }

    @Test
    public void testExtractAndReplaceString() {
        StringBuffer sb = new StringBuffer() ;
        Pattern p = Pattern.compile("\\{\\{(.*?)[\\|\\|.*?]?}}") ;
        String rawString = "<p>哈哈{{我:我,你,他}}，哈哈{{我:我,你,他组织}}哈看看</p>\",\"zh_CN\":\"<p>哈哈<em>{{我:我,你,他}}</em>，哈哈<strong>{{我:我,你,他组织}}</strong>哈看看</p>";
        System.out.println(rawString);
        Matcher m = p.matcher(rawString) ;
        while( m.find() ){
            String tmp = m.group() ;
            System.out.println(tmp);
            String v = "要替换成字符串";
            String[] strArr =  tmp.split(":");
            v = v.replace("\\", "\\\\").replace("$", "\\$");
            //替换掉查找到的字符串
            m.appendReplacement(sb, strArr[0]);
        }
        //别忘了加上最后一点
        m.appendTail(sb) ;
        System.out.println(sb);
        String s = sb.toString();
        System.out.println(s.replaceAll("\\{", ""));

    }

    @Test
    public void testLoop() {
        for (int i =0; i < Integer.MAX_VALUE; i ++) {
//            System.out.println(i);
        }
    }

    @Test
    public void testParseDetailedScore() {
        String str = "{\"detailedScoreConfig\":[{\"name\":{\"zh_CN\":\"领导自我\"},\"isSelect\":true,\"detailedScoreChildDimensions\":[{\"code\":\"360-PY-KYNL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"抗压能力\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-TZSY\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"调整适应\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-LHBT\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"灵活变通\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JJZD\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"积极主动\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JZJY\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"尽职敬业\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-ZXGG\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"自信果敢\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-SJGL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"时间管理\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-XXCZ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"学习成长\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-XTSW\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"系统思维\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-ZWRZ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"自我认知\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-KFBR\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"开放包容\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-ZQZY\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"追求专业\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-CXZZ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"诚信正直\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-QXGL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"情绪管理\"},\"type\":\"DIMENSION_AVERAGE\"}]},{\"name\":{\"en_US\":\"\",\"zh_CN\":\"领导他人\"},\"isSelect\":true,\"detailedScoreChildDimensions\":[{\"code\":\"360-PY-GXGT\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"高效沟通\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-RWWP\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"任务委派\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JLFD\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"教练辅导\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-TDNJ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"团队凝聚\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-SRYR\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"识人用人\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-TDJS\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"梯队建设\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JLRK\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"激励认可\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JLXR\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"建立信任\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JYGX\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"经营关系\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-SQWZ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"授权委责\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-DZRJWL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"打造人际网络\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-YXSF\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"影响说服\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-YJYL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"愿景引领\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-XTHZ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"协同合作\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-CTGL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"冲突管理\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-CJHZ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"促进合作\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-FZDYX\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"发展多样性\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-PYTR\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"培养他人\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-TDGH\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"团队规划\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JXGL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"绩效管理\"},\"type\":\"DIMENSION_AVERAGE\"}]},{\"name\":{\"en_US\":\"\",\"zh_CN\":\"完成当前业务\"},\"isSelect\":true,\"detailedScoreChildDimensions\":[{\"code\":\"360-PY-JHAP\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"计划安排\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-FXBK\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"风险把控\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JCPD\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"决策判断\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-GHBS\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"规划部署\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-SCDC\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"市场洞察\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-ZYGL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"资源管理\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-GZKH\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"关注客户\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-QBZX\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"确保执行\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-YKHWZX\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"以客户为中心\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JLGZGF\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"建立工作规范\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-SJMR\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"数据敏锐\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-WTJJ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"问题解决\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-ZQGZZL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"追求工作质量\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-CWMR\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"财务敏锐\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JGDX\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"结果导向\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-QJYS\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"全局意识\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-ZZMR\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"组织敏锐\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-GXZX\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"高效执行\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-GZSC\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"关注市场\"},\"type\":\"DIMENSION_AVERAGE\"}]},{\"name\":{\"en_US\":\"\",\"zh_CN\":\"关注未来业务\"},\"isSelect\":true,\"detailedScoreChildDimensions\":[{\"code\":\"360-PY-CXGS\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"持续改善\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-CDZZWBJ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"倡导组织无边界\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-TSZZNL\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"提升组织能力\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-DZXXXZZ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"打造学习型组织\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-JYBQD\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"驾驭不确定\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-QQSY\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"全球视野\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-SYRZ\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"商业睿智\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-ZLSW\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"战略思维\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-TDCX\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"推动创新\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-TDBG\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"推动变革\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-YLBG\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"引领变革\"},\"type\":\"DIMENSION_AVERAGE\"},{\"code\":\"360-PY-CXTP\",\"isSelected\":false,\"name\":{\"en_US\":\"\",\"zh_CN\":\"创新突破\"},\"type\":\"DIMENSION_AVERAGE\"}]}]}";
        JSONArray jsonArray = JSONArray.parseObject(str).getJSONArray("detailedScoreConfig");
        for (Object o : jsonArray) {
            JSONObject jsonObject = (JSONObject) o;
            JSONArray sonJsonArray = jsonObject.getJSONArray("detailedScoreChildDimensions");
            for (Object o2 : sonJsonArray) {
                JSONObject jsonObject2 = (JSONObject) o2;
                System.out.println(jsonObject2.getString("code"));
            }
        }
    }

    @Test
    public void testParseAdaptationDetailed() {
        String str = "{\"adaptationDetailsConfig\":[{\"score\":0.0,\"adaptationDetailsChildDimension\":[{\"code\":\"FXX-5L-EPA-010\",\"optimalScore\":5.52,\"name\":{\"en_US\":\"\",\"zh_CN\":\"分析性\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":7.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":2.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":2.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":7.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"HSX-5L-EPA-024\",\"optimalScore\":5.47,\"name\":{\"en_US\":\"\",\"zh_CN\":\"好胜心\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":7.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":2.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":2.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":7.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"KYX-5L-EPA-005\",\"optimalScore\":5.75,\"name\":{\"en_US\":\"\",\"zh_CN\":\"抗压性\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":7.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":8.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":2.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":7.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"LLCD-5L-EPA-016\",\"optimalScore\":4.91,\"name\":{\"en_US\":\"\",\"zh_CN\":\"理论思维\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":6.0,\"segmented\":\"HIGH\",\"lowScore\":3.0},{\"highScore\":2.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":8.0},{\"highScore\":3.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":2.0},{\"highScore\":8.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":6.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"QXKZ-5L-EPA-022\",\"optimalScore\":7.52,\"name\":{\"en_US\":\"\",\"zh_CN\":\"情绪控制\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":9.0,\"segmented\":\"HIGH\",\"lowScore\":6.0},{\"highScore\":5.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":10.0},{\"highScore\":6.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":5.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":9.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"SJDL-5L-EPA-006\",\"optimalScore\":6.94,\"name\":{\"en_US\":\"\",\"zh_CN\":\"社交胆量\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":4.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":10.0},{\"highScore\":5.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":4.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"TLX-5L-EPA-001\",\"optimalScore\":6.53,\"name\":{\"en_US\":\"\",\"zh_CN\":\"同理心\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":10.0},{\"highScore\":5.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"XRG-5L-EPA-002\",\"optimalScore\":6.05,\"name\":{\"en_US\":\"\",\"zh_CN\":\"信任感\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"XXL-5L-EPA-013\",\"optimalScore\":5.83,\"name\":{\"en_US\":\"\",\"zh_CN\":\"想象力\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":7.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":7.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"TLX-5L-EPA-009\",\"optimalScore\":6.06,\"name\":{\"en_US\":\"\",\"zh_CN\":\"条理性\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":5.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"ZRG-5L-EPA-008\",\"optimalScore\":7.86,\"name\":{\"en_US\":\"\",\"zh_CN\":\"责任感\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":9.0,\"segmented\":\"HIGH\",\"lowScore\":7.0},{\"highScore\":6.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":10.0},{\"highScore\":7.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":6.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":9.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"ZXX-5L-EPA-018\",\"optimalScore\":6.5,\"name\":{\"en_US\":\"\",\"zh_CN\":\"自信心\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":4.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":5.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":4.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"HZ-5L-EPA-012\",\"optimalScore\":5.68,\"name\":{\"en_US\":\"\",\"zh_CN\":\"合作\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":7.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":7.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"RQLD-5L-EPA-015\",\"optimalScore\":6.6,\"name\":{\"en_US\":\"\",\"zh_CN\":\"人情练达\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":10.0},{\"highScore\":4.19,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"ZPQX-5L-EPA-011\",\"optimalScore\":6.45,\"name\":{\"en_US\":\"\",\"zh_CN\":\"支配倾向\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":10.0},{\"highScore\":5.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"QX-5L-EPA-014\",\"optimalScore\":5.73,\"name\":{\"en_US\":\"\",\"zh_CN\":\"谦虚\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":7.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":7.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"SJDX-5L-EPA-004\",\"optimalScore\":5.32,\"name\":{\"en_US\":\"\",\"zh_CN\":\"数据导向\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":7.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":2.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":2.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":7.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"JSGB-5L-EPA-019\",\"optimalScore\":4.08,\"name\":{\"en_US\":\"\",\"zh_CN\":\"接受改变\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":6.0,\"segmented\":\"HIGH\",\"lowScore\":2.0},{\"highScore\":1.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":8.0},{\"highScore\":2.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":1.0},{\"highScore\":8.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":6.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"ZZXJ-5L-EPA-020\",\"optimalScore\":6.22,\"name\":{\"en_US\":\"\",\"zh_CN\":\"注重细节\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":5.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"GWX-5L-EPA-023\",\"optimalScore\":5.5,\"name\":{\"en_US\":\"\",\"zh_CN\":\"敢为性\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":7.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":2.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":2.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":7.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"GD-5L-EPA-007\",\"optimalScore\":5.93,\"name\":{\"en_US\":\"\",\"zh_CN\":\"果断\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":8.0,\"segmented\":\"HIGH\",\"lowScore\":4.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":9.0},{\"highScore\":4.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0},{\"highScore\":9.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":8.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"JLSP-5L-EPA-017\",\"optimalScore\":6.99,\"name\":{\"en_US\":\"\",\"zh_CN\":\"精力水平\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":9.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":4.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":10.0},{\"highScore\":5.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":4.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":9.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85},{\"code\":\"YXSF-5L-EPA-003\",\"optimalScore\":6.91,\"name\":{\"en_US\":\"\",\"zh_CN\":\"影响说服\"},\"model\":\"FIVE_SECTIONS\",\"segmented\":[{\"highScore\":9.0,\"segmented\":\"HIGH\",\"lowScore\":5.0},{\"highScore\":4.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_LOW\",\"lowScore\":10.0},{\"highScore\":5.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":4.0},{\"highScore\":10.0,\"segmented\":\"RIGHT_MEDIUM\",\"lowScore\":9.0}],\"type\":\"STANDARD_DEVIATION\",\"weights\":3.85}],\"parentDimensionDescription\":{\"zh_CN\":\"\"},\"parentDimension\":{\"zh_CN\":\"职业性格\"},\"type\":\"NOT\"},{\"score\":0.0,\"adaptationDetailsChildDimension\":[{\"code\":\"LJFX-5FC-AT-037-TIP\",\"optimalScore\":10.0,\"name\":{\"en_US\":\"\",\"zh_CN\":\"逻辑分析能力\"},\"model\":\"RIGHT_THREE_SECTIONS\",\"segmented\":[{\"highScore\":10.0,\"segmented\":\"HIGH\",\"lowScore\":7.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":7.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0}],\"type\":\"NORM\",\"weights\":3.85},{\"code\":\"SZFX-5FC-AT-039\",\"optimalScore\":10.0,\"name\":{\"en_US\":\"\",\"zh_CN\":\"数字分析能力\"},\"model\":\"RIGHT_THREE_SECTIONS\",\"segmented\":[{\"highScore\":10.0,\"segmented\":\"HIGH\",\"lowScore\":7.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":7.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0}],\"type\":\"NORM\",\"weights\":3.85},{\"code\":\"WZFX-3YN-AT-038\",\"optimalScore\":10.0,\"name\":{\"en_US\":\"\",\"zh_CN\":\"文字分析能力\"},\"model\":\"RIGHT_THREE_SECTIONS\",\"segmented\":[{\"highScore\":10.0,\"segmented\":\"HIGH\",\"lowScore\":7.0},{\"highScore\":3.0,\"segmented\":\"LEFT_LOW\",\"lowScore\":1.0},{\"highScore\":7.0,\"segmented\":\"LEFT_MEDIUM\",\"lowScore\":3.0}],\"type\":\"NORM\",\"weights\":3.85}],\"parentDimensionDescription\":{\"zh_CN\":\"\"},\"parentDimension\":{\"zh_CN\":\"基础能力\"},\"type\":\"NOT\"}]}";
        JSONArray jsonArray = JSONArray.parseObject(str).getJSONArray("adaptationDetailsConfig");
        for (Object o : jsonArray) {
            JSONObject jsonObject = (JSONObject) o;
            JSONArray sonJsonArray = jsonObject.getJSONArray("adaptationDetailsChildDimension");
            for (Object o2 : sonJsonArray) {
                JSONObject jsonObject2 = (JSONObject) o2;
                System.out.println("--" + jsonObject2.getString("weights"));
            }
        }
    }

    @Test
    public void test() {
        String text = "Sheet1!$B$2:$B$7";
        Pattern p = Pattern.compile("[\\d]");
        Matcher matcher = p.matcher(text);
        String result = matcher.replaceAll("");
        System.out.println(result);
        System.out.println(text.replace("\\d", ""));

    }

    @Test
    public void test2() {
        String text = "Sheet1!$B$2:$B$7";
        Pattern p = Pattern.compile("\\$[A-Z]\\$");
        Matcher matcher = p.matcher(text);
        System.out.println(matcher.toString());
        if (matcher.find()) {
            System.out.println(matcher.group());
        }
//        String result = matcher.replaceAll("");
//        System.out.println(result);

    }

    @Test
    public void testPrintSql() {
        Map<String, String> map = new HashMap<>();
        map.put("1", "11");
        map.put("2", "11");
        map.put("3", "112");
        map.put("4", "113");
        Map<String, String> newMap = map.entrySet().stream().filter(n -> n.getValue().equals("11")).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        System.out.println(newMap.toString());
    }

    @Test
    public void testParseJson() {
        String str = "{\"personId\":\"1431080840577810434\",\"projectId\":\"1431077518416412674\"}";
        CreateReportVO createReportVO = JSON.parseObject(str, CreateReportVO.class);
        System.out.println(createReportVO.getProjectId());
    }

    @Test
    public void testEmptyList() {
        List<String> list = new ArrayList<>();
        List<String> group = new ArrayList<>();
        group.addAll(list);
        System.out.println(group.size());
    }

    @Test
    public void removeJson() {
        StringBuilder rawJson = new StringBuilder("");

        JSONObject jsonObject = JSONObject.parseObject(rawJson.toString());
        JSONArray assessmentInfos = jsonObject.getJSONArray("assessmentInfos");
        for (Object o : assessmentInfos) {
            JSONObject assessmentInfo = (JSONObject) o;
            JSONArray detailList = assessmentInfo.getJSONArray("detailList");
            for (Object o2 : detailList) {
                JSONObject detail = (JSONObject) o2;
                detail.remove("projectName");
                detail.remove("key");
                detail.remove("nairName");
                detail.remove("time");
                detail.remove("name");
            }
        }
        System.out.println(jsonObject.toJSONString());

    }


    @Test
    public void testLetter() {
        System.out.println((char)97);//a
        System.out.println((char)122);//z
        System.out.println((char)65);//A
        System.out.println((char)90);//Z

        //将字母转为数字
        System.out.println("Z".getBytes()[0]);//90

        //48-57对应0...9
        System.out.println((char)48);
        System.out.println((char)57);

        // 65
        System.out.println("A".getBytes()[0]);
        // 90
        System.out.println("Z".getBytes()[0]);
        System.out.println(digitToCapitalLetter(0));
    }

    private String digitToCapitalLetter(int digit) {
        return String.valueOf((char) (digit + 65));
    }

    @Test
    public void testTimestamp() {
        System.out.println(System.currentTimeMillis());
    }

    @Test
    public void testRetainAll() {
        List<String> list1 = new ArrayList<>();
        list1.add("A");
        list1.add("B");
        list1.add("C");

        List<String> list2 = new ArrayList<>();
        list2.add("D");
        list2.add("B");
        list2.add("C");

        list1.retainAll(list2);
        System.out.println(list1.toString());
    }

    @Test
    public void testReplace() {
        String content = "<p>您好：<br />诚挚邀请您参与本次活动。<br />请您在{{开始日期}}和{{结束日期}}之间登录{{链接}}进行线上填答<span style=\\\"color: #ff0000;\\\">（建议使用Google Chrome浏览器填答）。</span><br />请您预留好充足的时间一次性完成，谢谢。</p>\\n<p><img src=\\\"../../api/file/www/1483672949457289218\\\" alt=\\\"\\\" width=\\\"247\\\" height=\\\"187\\\" /></p>";
        content = content.replace("../../api/file/www/", "http://10.55.8.171/api/file/www/");
        System.out.println(content);
    }

    @Test
    public void testRetainAllTime() {
        List<String> list1 = new ArrayList<>();
        for (int i =0; i < 50000; i ++) {
            list1.add(i + "");
        }

        List<String> list2 = new ArrayList<>();
        for (int i =0; i < 50000; i ++) {
            list2.add(i + "");
        }
        long startTime = System.currentTimeMillis();
        list1.retainAll(list2);
        System.out.println("cost time=" + (System.currentTimeMillis() - startTime));
        startTime = System.currentTimeMillis();
        List<String> accountIdList = list1.stream().filter(list2::contains).collect(Collectors.toList());
        System.out.println("cost time=" + (System.currentTimeMillis() - startTime));
        retainAllByGuava(list1, list2);
        System.out.println(list1.toString());
    }

    private static void retainAllByGuava(List<String> list1, List<String> list22) {


        long begin = System.currentTimeMillis();

        Set list = new HashSet<>(list1);

        Set list2 = new HashSet<>(list22);

        Sets.SetView intersection = Sets.intersection(list, list2);

        long end = System.currentTimeMillis();

        System.out.println("guava方法耗时:" + (end - begin));

        System.out.println("交集的个数为:" + intersection.size());

    }

    @Test
    public void testFilter2() {
//        List<String> list1 = new ArrayList<>();
//        list1.add("A");
//        list1.add("B");
//        list1.add("C");
//
//        List<String> list2 = new ArrayList<>();
//        list2.add("D");
//        list2.add("B");
//        list2.add("C");
//        Predicate<String> notIn2 = s -> list2.stream().anyMatch(mc -> s.equals(mc));
//        List<String> list3 = list1.stream().filter(notIn2).collect(Collectors.toList());
//        System.out.println(list3);
//        Integer i1 = new Integer(385);
//        Integer i2 = new Integer(380);
//        System.out.println(i1.equals(i2));
//        System.out.println(Objects.equals(i1, i2));
//
//        Integer i3 = new Integer(385);
//        Integer i4 = new Integer(385);
//        System.out.println(i3.equals(i4));
//        System.out.println(Objects.equals(i3, i4));

        String str = "p2-overview-demographic-1-answer-pie-chart";
        Pattern p = Pattern.compile("p2-overview-demographic-.*-answer-pie-chart");
        Matcher m = p.matcher(str);
        boolean result = m.find();
        System.out.println(result);
    }
}
