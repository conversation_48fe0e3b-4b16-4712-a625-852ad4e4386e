import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;

public class CreateTestExcelForPairs {
    public static void main(String[] args) {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        
        // 创建工作表，名称以"数据汇总表"开头
        Sheet sheet = workbook.createSheet("数据汇总表_测试");
        
        // 创建行和单元格
        // 第一行 (索引0)
        Row row0 = sheet.createRow(0);
        row0.createCell(0).setCellValue("标题1");
        row0.createCell(1).setCellValue("标题2");
        row0.createCell(2).setCellValue("标题3");
        row0.createCell(3).setCellValue("标题4");
        row0.createCell(4).setCellValue("标题5");
        row0.createCell(5).setCellValue("数据列");
        
        // 第二行 (索引1)
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue("数据");
        row1.createCell(1).setCellValue("数据");
        row1.createCell(2).setCellValue("数据");
        row1.createCell(3).setCellValue("数据");
        row1.createCell(4).setCellValue("数据");
        row1.createCell(5).setCellValue("值A");
        
        // 第三行 (索引2)
        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue("数据");
        row2.createCell(1).setCellValue("数据");
        row2.createCell(2).setCellValue("数据");
        row2.createCell(3).setCellValue("数据");
        row2.createCell(4).setCellValue("数据");
        row2.createCell(5).setCellValue("值A");  // 与第2行相同
        
        // 第四行 (索引3)
        Row row3 = sheet.createRow(3);
        row3.createCell(0).setCellValue("数据");
        row3.createCell(1).setCellValue("数据");
        row3.createCell(2).setCellValue("数据");
        row3.createCell(3).setCellValue("数据");
        row3.createCell(4).setCellValue("数据");
        row3.createCell(5).setCellValue("值B");
        
        // 第五行 (索引4)
        Row row4 = sheet.createRow(4);
        row4.createCell(0).setCellValue("数据");
        row4.createCell(1).setCellValue("数据");
        row4.createCell(2).setCellValue("数据");
        row4.createCell(3).setCellValue("数据");
        row4.createCell(4).setCellValue("数据");
        row4.createCell(5).setCellValue("值C");  // 与第4行不同
        
        // 第六行 (索引5)
        Row row5 = sheet.createRow(5);
        row5.createCell(0).setCellValue("数据");
        row5.createCell(1).setCellValue("数据");
        row5.createCell(2).setCellValue("数据");
        row5.createCell(3).setCellValue("数据");
        row5.createCell(4).setCellValue("数据");
        row5.createCell(5).setCellValue("值D");
        
        // 第七行 (索引6)
        Row row6 = sheet.createRow(6);
        row6.createCell(0).setCellValue("数据");
        row6.createCell(1).setCellValue("数据");
        row6.createCell(2).setCellValue("数据");
        row6.createCell(3).setCellValue("数据");
        row6.createCell(4).setCellValue("数据");
        row6.createCell(5).setCellValue("值D");  // 与第6行相同
        
        // 第八行 (索引7)
        Row row7 = sheet.createRow(7);
        row7.createCell(0).setCellValue("数据");
        row7.createCell(1).setCellValue("数据");
        row7.createCell(2).setCellValue("数据");
        row7.createCell(3).setCellValue("数据");
        row7.createCell(4).setCellValue("数据");
        row7.createCell(5).setCellValue("值E");
        
        // 第九行 (索引8)
        Row row8 = sheet.createRow(8);
        row8.createCell(0).setCellValue("数据");
        row8.createCell(1).setCellValue("数据");
        row8.createCell(2).setCellValue("数据");
        row8.createCell(3).setCellValue("数据");
        row8.createCell(4).setCellValue("数据");
        row8.createCell(5).setCellValue("值F");  // 与第8行不同
        
        // 自动调整列宽
        for (int i = 0; i < 6; i++) {
            sheet.autoSizeColumn(i);
        }
        
        // 写入文件
        try (FileOutputStream outputStream = new FileOutputStream("test-data-pairs.xlsx")) {
            workbook.write(outputStream);
            workbook.close();
            System.out.println("Excel文件创建成功：test-data-pairs.xlsx");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}