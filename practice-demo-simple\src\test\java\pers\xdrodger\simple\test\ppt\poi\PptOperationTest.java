package pers.xdrodger.simple.test.ppt.poi;

import com.alibaba.fastjson.JSONObject;
import org.apache.poi.common.usermodel.fonts.FontGroup;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.ooxml.POIXMLDocumentPart;
import org.apache.poi.sl.usermodel.GroupShape;
import org.apache.poi.sl.usermodel.TextParagraph;
import org.apache.poi.sl.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xddf.usermodel.chart.Shape;
import org.apache.poi.xddf.usermodel.text.TextContainer;
import org.apache.poi.xslf.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.xmlbeans.XmlObject;
import org.junit.Before;
import org.junit.Test;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTChart;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPieChart;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPieSer;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPlotArea;
import org.openxmlformats.schemas.drawingml.x2006.main.CTRegularTextRun;
import org.openxmlformats.schemas.drawingml.x2006.main.CTTextCharacterProperties;
import org.openxmlformats.schemas.drawingml.x2006.main.CTTextFont;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTFont;

import java.awt.*;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

public class PptOperationTest {

    private XMLSlideShow ppt;

    private String fileName = "8 敬业度+3.0-报告样本.pptx";


    private boolean isWindows() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("windows")) {
            return true;
        }
        return false;
    }

    private String getInputFilePath() {
        if (isWindows()) {
            return "D:\\workspace\\practice-demo\\file\\input\\";
        }
        return "/Library/workspace/my-project/practice-demo/file/input/";
    }

    private String getOutputFilePath() {
        if (isWindows()) {
            return "D:\\workspace\\practice-demo\\file\\output\\";
        }
        return "/Library/workspace/my-project/practice-demo/file/output/";
    }

    @Before
    public void setUp() throws Exception {
        FileInputStream fis = new FileInputStream(getInputFilePath() + fileName);
        ppt = new XMLSlideShow(fis);
    }

    private void getByName(String shapeName) {
        for (XSLFSlide slide : ppt.getSlides()) {
             for (POIXMLDocumentPart.RelationPart relationPart : slide.getRelationParts()) {
                 POIXMLDocumentPart documentPart = relationPart.getDocumentPart();
                 if (documentPart instanceof XSLFChart) {
                     XSLFChart chart = (XSLFChart) documentPart;
//                     chart.getRelationParts().
                 }
//                 if (documentPart instanceof XSLFShape) {
//                     XSLFChart chart = (XSLFChart) documentPart;
//                 }
             }
        }
    }

    private XSLFTable getTableByName(String shapeName) {
        for (XSLFSlide slide : ppt.getSlides()) {
            for (XSLFShape shape : slide.getShapes()) {
                if (shape.getShapeName().equals(shapeName)) {
                    return (XSLFTable) shape;
                }
            }
        }
        return null;
    }

    private XSLFTable getChartByName(String shapeName) {
        for (XSLFSlide slide : ppt.getSlides()) {
            for (XSLFShape shape : slide.getShapes()) {
                if (shape.getShapeName().equals(shapeName)) {
                    return (XSLFTable) shape;
                }
            }
        }
        return null;
    }

    @Test
    public void readSlides() {
        List<XSLFSlide> slides = ppt.getSlides();
        for (XSLFSlide slide : slides) {
            List<XSLFShape> shapes = slide.getShapes();
            for (XSLFShape shape : shapes) {
                System.out.println(String.format("slideId=%s shapeId=%s shapeName=%s", slides.indexOf(slide) + 1, shape.getShapeId(), shape.getShapeName()));
            }
        }
    }

    private void addCenterText(XSLFTableCell cell, String text) {
        XSLFTextParagraph p = cell.addNewTextParagraph();
        p.setTextAlign(TextParagraph.TextAlign.CENTER);
        XSLFTextRun r = p.addNewTextRun();
        r.setText(text);
        r.setFontSize(14.0);
        r.setFontFamily(HSSFFont.FONT_ARIAL);
        r.setFontColor(Color.RED);
        cell.setText(text);
        //cell.setVerticalAlignment(VerticalAlignment.MIDDLE);
        cell.setFillColor(new Color(242, 242, 242));
    }

    @Test
    public void testAddRow2Table() throws Exception {
        XSLFTable t = getTableByName("dyssqk-table");
        List<XSLFTableRow> rows = t.getRows();
        for (XSLFTableRow row : rows ) {
            List<XSLFTableCell> cells = row.getCells();
            for (XSLFTableCell cell : cells) {
                System.out.println(cell.getText());
                System.out.println(cell.getFillColor());
                System.out.println(cell.getVerticalAlignment());
            }
        }
        XSLFTableRow newRow = t.addRow();
        XSLFTableCell timeCell = newRow.addCell();
        addCenterText(timeCell, "2021-04-20~2021-05-06");
        XSLFTableCell totalCountCell = newRow.addCell();
        addCenterText(totalCountCell, "888");
        XSLFTableCell validCountCell = newRow.addCell();
        addCenterText(validCountCell, "876");
        XSLFTableCell validPercentCell = newRow.addCell();
        addCenterText(validPercentCell, "98.67%");
        ppt.write(new FileOutputStream(getOutputFilePath() + fileName));
    }

    private Object getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case STRING:
                return cell.getStringCellValue();
            default:
                return cell.getStringCellValue();
        }
    }

    @Test
    public void readChart() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(13);
        for (POIXMLDocumentPart.RelationPart relationPart : slide.getRelationParts()) {
            POIXMLDocumentPart documentPart = relationPart.getDocumentPart();
            if (documentPart instanceof XSLFChart) {
                XSLFChart chart = (XSLFChart) documentPart;
                chart.setTitleText("hhhh");
                TextContainer textContainer = chart.getTitle().getBody().getParentShape();
                System.out.println("title=" + chart.getTitle().getBody().getParagraph(0).getText());
                System.out.println(chart.getTitleShape().getShapeName());
//                XSSFWorkbook workbook = chart.getWorkbook();
//                Iterator<Sheet> sheetIterator = workbook.sheetIterator();
//                while (sheetIterator.hasNext()) {
//                    Sheet sheet = sheetIterator.next();
//                    Iterator<Row> rowIterator = sheet.rowIterator();
//                    while (rowIterator.hasNext()) {
//                        Row row = rowIterator.next();
//                        Iterator<Cell> cellIterator = row.cellIterator();
//                        while (cellIterator.hasNext()) {
//                            Cell cell = cellIterator.next();
//                            System.out.println(getCellValue(cell));
//                        }
//                    }
//                }
            }
        }
        ppt.write(new FileOutputStream(getOutputFilePath() + fileName));
    }


    @Test
    public void readChart2() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(13);
        for (XSLFShape shape : slide.getShapes()) {
            if ("bmzcd-pie-chart".equals(shape.getShapeName())) {
                System.out.println(shape.getShapeName());
                System.out.println(shape.toString());
                if (shape instanceof XSLFGraphicFrame) {
                    System.out.println("XSLFGraphicFrame");
                    XSLFGraphicFrame graphicFrame = (XSLFGraphicFrame) shape;
                    XSLFChart chart = graphicFrame.getChart();
                    XSSFWorkbook workbook = chart.getWorkbook();
                    Iterator<Sheet> sheetIterator = workbook.sheetIterator();
                    while (sheetIterator.hasNext()) {
                        Sheet sheet = sheetIterator.next();
                        Iterator<Row> rowIterator = sheet.rowIterator();
                        while (rowIterator.hasNext()) {
                            Row row = rowIterator.next();
                            Iterator<Cell> cellIterator = row.cellIterator();
                            while (cellIterator.hasNext()) {
                                Cell cell = cellIterator.next();
                                System.out.println(getCellValue(cell));
                            }
                        }
                    }
                }
            }
        }

    }

    @Test
    public void readChart3() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(19);
        for (XSLFShape shape : slide.getShapes()) {
            if (shape instanceof XSLFTable) {
                System.out.println(shape.getShapeName());
            }
            if (shape instanceof XSLFGraphicFrame && !(shape instanceof XSLFTable)) {
                System.out.println(shape.getShapeName());
                XSLFGraphicFrame graphicFrame = (XSLFGraphicFrame) shape;
                if (!graphicFrame.hasChart()) {
                    continue;
                }
                XSLFChart chart = graphicFrame.getChart();
                XSSFWorkbook workbook = chart.getWorkbook();
                Iterator<Sheet> sheetIterator = workbook.sheetIterator();
                while (sheetIterator.hasNext()) {
                    Sheet sheet = sheetIterator.next();
                    Iterator<Row> rowIterator = sheet.rowIterator();
                    while (rowIterator.hasNext()) {
                        Row row = rowIterator.next();
                        Iterator<Cell> cellIterator = row.cellIterator();
                        while (cellIterator.hasNext()) {
                            Cell cell = cellIterator.next();
                            System.out.println(getCellValue(cell));
                        }
                    }
                }
            }
        }

    }

    @Test
    public void readChart4() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(13);
        for (XSLFShape shape : slide.getShapes()) {
            if (shape instanceof XSLFTable) {
                System.out.println(shape.getShapeName());
            }
            if (shape instanceof XSLFGraphicFrame && !(shape instanceof XSLFTable)) {
                System.out.println(shape.getShapeName());
                XSLFGraphicFrame graphicFrame = (XSLFGraphicFrame) shape;
                if (!graphicFrame.hasChart()) {
                    continue;
                }
                XSLFChart chart = graphicFrame.getChart();
                CTChart ctChart = chart.getCTChart();
                CTPlotArea plotArea = ctChart.getPlotArea();
                plotArea.getCatAxList();


                for (XDDFChartData chartData : chart.getChartSeries()) {
                    System.out.println(chartData.getSeriesCount());
                    for (int i =0; i < chartData.getSeriesCount(); i ++) {
                        chartData.getSeries();
                    }
                    for (XDDFChartData.Series series : chartData.getSeries()) {
                        XDDFDataSource xddfDataSource = series.getCategoryData();
                        System.out.println(xddfDataSource.getDataRangeReference());
                        XDDFNumericalDataSource numericalDataSource = series.getValuesData();
                        System.out.println(numericalDataSource.getDataRangeReference());
                    }
                    System.out.println(JSONObject.toJSONString(chartData.getCategoryAxis()));
//                    List<XDDFValueAxis> valueAxes = chartData.getValueAxes();
//                    for (XDDFValueAxis valueAxis : valueAxes) {
//                        System.out.println(JSONObject.toJSONString(valueAxis));
//                    }
                }
            }
        }

    }

    @Test
    public void addDataToChart() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(1);
        for (XSLFShape shape : slide.getShapes()) {
            if (shape instanceof XSLFGraphicFrame && !(shape instanceof XSLFTable)) {
                System.out.println(shape.getShapeName());
                XSLFGraphicFrame graphicFrame = (XSLFGraphicFrame) shape;
                XSLFChart chart = graphicFrame.getChart();
                XSSFWorkbook workbook = chart.getWorkbook();
                XSSFSheet sheet = workbook.getSheetAt(0);
                Row row = sheet.createRow(sheet.getLastRowNum() + 1);
                row.createCell(0).setCellValue("技术部");
                row.createCell(1).setCellValue("50");
                CTPlotArea plot = chart.getCTChart().getPlotArea();
                workbook.write(chart.getPackagePart().getOutputStream());
//                CTPieChart pieChart = plot.getPieChartArray(0);
//                int i = 0;
//                for (CTPieSer ser : pieChart.getSerList()) {
////                    updateChartCatAndNum(seriesDatas.get(i), ser.getTx(), ser.getCat(), ser.getVal());
////                    ++i;
//                }
            }
        }
        ppt.write(new FileOutputStream(getOutputFilePath() + fileName));

    }

    @Test
    public void addPieChart() throws Exception {
        XSLFSlide slide = ppt.getSlides().get(14);
        XSLFChart chart = slide.getSlideShow().createChart(slide);


        ppt.write(new FileOutputStream(getOutputFilePath() + fileName));

    }
}
