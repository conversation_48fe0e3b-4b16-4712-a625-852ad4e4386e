package pers.xdrodger.simple.test.ppt.apose;

import com.spire.data.table.DataColumn;
import com.spire.data.table.DataRow;
import com.spire.data.table.DataTable;
import com.spire.pdf.tables.table.DataTypes;
import com.spire.presentation.charts.ChartDataLabelPosition;
import com.spire.presentation.charts.ChartType;
import com.spire.presentation.charts.IChart;
import com.spire.presentation.charts.entity.CellRange;
import com.spire.presentation.charts.entity.ChartCategory;
import com.spire.presentation.charts.entity.ChartDataLabel;
import com.spire.presentation.charts.entity.ChartSeriesDataFormat;
import com.spire.presentation.drawing.FillFormatType;
import com.spire.presentation.drawing.PatternFillType;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.awt.*;
import java.util.Iterator;

public class OperationCultureAnswerBarChartTest {

    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-柱状图长度问题.pptx");
        IChart chart = prismaPptUtil.getChart("p1-preface-answer-bar-chart");
        Iterator<ChartCategory> categoryIterator = chart.getCategories().iterator();
        while (categoryIterator.hasNext()) {
            ChartCategory category = categoryIterator.next();
            System.out.println(category.getDataRange().getText());
            category.getDataRange().setText(category.getDataRange().getText() + 2);
        }
        DataTable dataTable = new DataTable();
        dataTable.getColumns().add(new DataColumn("名称", DataTypes.DATATABLE_STRING));
        dataTable.getColumns().add(new DataColumn("人数", DataTypes.DATATABLE_INT));

        DataRow row1 = dataTable.newRow();
        row1.setString("名称", "邀请人数");
        row1.setInt("人数", 20);
        DataRow row2 = dataTable.newRow();
        row2.setString("名称", "完成人数");
        row2.setInt("人数", 19);
        DataRow row3 = dataTable.newRow();
        row3.setString("名称", "有效人数");
        row3.setInt("人数", 19);
        dataTable.getRows().add(row1);
        dataTable.getRows().add(row2);
        dataTable.getRows().add(row3);

        //将数据写入图表
        for (int c = 0; c < dataTable.getColumns().size(); c++) {
            chart.getChartData().get(0, c).setText(dataTable.getColumns().get(c).getColumnName());
        }
        for (int r = 0; r < dataTable.getRows().size(); r++) {
            Object[] datas = dataTable.getRows().get(r).getArrayList();
            for (int c = 0; c < datas.length; c++) {
                chart.getChartData().get(r + 1, c).setValue(datas[c]);

            }
        }
        //设置系列标签
        chart.getSeries().setSeriesLabel(chart.getChartData().get("B1", "B1"));

        //设置类别标签
        chart.getCategories().setCategoryLabels(chart.getChartData().get("A2", "A4"));

        //为各个系列赋值
        chart.getSeries().get(0).setValues(chart.getChartData().get("B2", "B4"));

        for (int i = 0; i < dataTable.getColumns().size() - 1; i ++) {
            for (int j =0; j < dataTable.getRows().size(); j ++) {
                DataRow dataRow = dataTable.getRows().get(j);
                Object[] objects = dataRow.getArrayList();
                Integer val = (Integer) objects[i+1];
                ChartDataLabel dataLabel = chart.getSeries().get(i).getDataLabels().add();
                //显示标签的值
                dataLabel.setLabelValueVisible(true);
                dataLabel.setPosition(ChartDataLabelPosition.OUTSIDE_END);
                //修改数据标签数值
                dataLabel.getTextFrame().getParagraphs().get(0).setText(String.valueOf(val));
                dataLabel.getTextFrame().getParagraphs().get(0).getDefaultCharacterProperties().getFill().setFillType(FillFormatType.SOLID);
                dataLabel.getTextFrame().getParagraphs().get(0).getDefaultCharacterProperties().getFill().getSolidColor().setColor(Color.black);
                dataLabel.getTextFrame().getParagraphs().get(0).getDefaultCharacterProperties().setFontHeight(10);
            }
        }

        chart.getSeries().get(0).getFill().setFillType(FillFormatType.PATTERN);
        chart.getSeries().get(0).getFill().getPattern().setPatternType(PatternFillType.NARROW_VERTICAL);
        chart.getSeries().get(0).getFill().getPattern().getForegroundColor().setColor(new Color(237, 125, 49));


        //保存文件
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-柱状图长度问题-output.pptx");
    }

}
