<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="db72e8c8-cb15-4a6a-99e4-f81090aae3da" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/CrossAnaChartReplace.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/CustomChartReplace.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/ReplaceFrontChart.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace10.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace14.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace15.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace16.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace17.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace18.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace2.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace20.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace21.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace22.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace23.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace25.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace26.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace27.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace28.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace29.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace3.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace30.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace31.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace32.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace33.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace34.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace35.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace36.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace37.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace38.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace39.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace40.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace41.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace42.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace5.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace6.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace7.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace8.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace9.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/vo/SurveyField.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/FileUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/FileUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/vo/PptTableCellData.java" beforeDir="false" afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/vo/PptTableCellData.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/vo/PptTableRowData.java" beforeDir="false" afterPath="$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/vo/PptTableRowData.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/practice-demo-util/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/practice-demo-util/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\file\maven\repository" />
        <option name="userSettingsFile" value="D:\software\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="PhpDebugGeneral">
    <xdebug_debug_ports port="9000" />
    <xdebug_debug_ports port="9003" />
    <xdebug_debug_ports port="55669" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2hlh0qEco4B27ZHjI6flnaEBBfW" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.ExcelDataValidator.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.TextPartReplace39.textPartReplace.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.TextPartReplace40.textPartReplace.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.TextPartReplace41.textPartReplace.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.TextPartReplace42.textPartReplace.executor&quot;: &quot;Run&quot;,
    &quot;Maven.practice-demo [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.practice-demo [compile].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/file/knx/code/practice-demo&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;8d7fb224f5d4698fc7600e3ae4605191&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\file\knx\code\practice-demo\practice-demo-simple\src\test\java\pers\xdrodger\simple\test\ppt\poi\practice" />
      <recent name="D:\file\knx\code\practice-demo\practice-demo-simple\src\test\java\pers\xdrodger\simple\test\ppt\poi\vo" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="pers.xdrodger.simple.test.ppt.poi.practice" />
    </key>
  </component>
  <component name="RunManager" selected="Application.ExcelFileMigrationUtil">
    <configuration name="ExcelDataValidator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="pers.xdrodger.util.ExcelDataValidator" />
      <module name="practice-demo-util" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="pers.xdrodger.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ExcelFileMigrationUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="pers.xdrodger.util.ExcelFileMigrationUtil" />
      <module name="practice-demo-util" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="pers.xdrodger.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TextPartReplace40.textPartReplace" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="practice-demo-simple" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="pers.xdrodger.simple.test.ppt.poi.practice.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="pers.xdrodger.simple.test.ppt.poi.practice" />
      <option name="MAIN_CLASS_NAME" value="pers.xdrodger.simple.test.ppt.poi.practice.TextPartReplace40" />
      <option name="METHOD_NAME" value="textPartReplace" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TextPartReplace41.textPartReplace" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="practice-demo-simple" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="pers.xdrodger.simple.test.ppt.poi.practice.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="pers.xdrodger.simple.test.ppt.poi.practice" />
      <option name="MAIN_CLASS_NAME" value="pers.xdrodger.simple.test.ppt.poi.practice.TextPartReplace41" />
      <option name="METHOD_NAME" value="textPartReplace" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TextPartReplace42.textPartReplace" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="practice-demo-simple" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="pers.xdrodger.simple.test.ppt.poi.practice.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="pers.xdrodger.simple.test.ppt.poi.practice" />
      <option name="MAIN_CLASS_NAME" value="pers.xdrodger.simple.test.ppt.poi.practice.TextPartReplace42" />
      <option name="METHOD_NAME" value="textPartReplace" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="practice-demo-simple" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="practice-demo-simple" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ExcelFileMigrationUtil" />
        <item itemvalue="JUnit.TextPartReplace42.textPartReplace" />
        <item itemvalue="Application.ExcelDataValidator" />
        <item itemvalue="JUnit.TextPartReplace41.textPartReplace" />
        <item itemvalue="JUnit.TextPartReplace40.textPartReplace" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="db72e8c8-cb15-4a6a-99e4-f81090aae3da" name="Changes" comment="" />
      <created>1718174350532</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1718174350532</updated>
      <workItem from="1718174351789" duration="192000" />
      <workItem from="1718174705445" duration="1763000" />
      <workItem from="1718176546536" duration="9209000" />
      <workItem from="1719459020094" duration="2000" />
      <workItem from="1719459042356" duration="10234000" />
      <workItem from="1719804731350" duration="1470000" />
      <workItem from="1720064143497" duration="601000" />
      <workItem from="1721294401434" duration="1180000" />
      <workItem from="1721371238503" duration="2074000" />
      <workItem from="1721614042239" duration="2361000" />
      <workItem from="1721638466548" duration="816000" />
      <workItem from="1721873339486" duration="477000" />
      <workItem from="1722910671191" duration="1014000" />
      <workItem from="1722913590488" duration="11390000" />
      <workItem from="1723196638028" duration="1867000" />
      <workItem from="1723448096999" duration="4225000" />
      <workItem from="1723534678743" duration="2259000" />
      <workItem from="1723546999158" duration="949000" />
      <workItem from="1723606369717" duration="12112000" />
      <workItem from="1724032912603" duration="1910000" />
      <workItem from="1724036252705" duration="6555000" />
      <workItem from="1724314953098" duration="4605000" />
      <workItem from="1724378998501" duration="66000" />
      <workItem from="1724400761650" duration="1740000" />
      <workItem from="1724665389239" duration="1722000" />
      <workItem from="1724744138232" duration="1214000" />
      <workItem from="1724822403210" duration="1720000" />
      <workItem from="1726112461369" duration="68000" />
      <workItem from="1726112917513" duration="4061000" />
      <workItem from="1726133442425" duration="1311000" />
      <workItem from="1726216297213" duration="1645000" />
      <workItem from="1729157564878" duration="3876000" />
      <workItem from="1729220555244" duration="1854000" />
      <workItem from="1729837539093" duration="229000" />
      <workItem from="1729842069441" duration="1772000" />
      <workItem from="1730186260650" duration="1057000" />
      <workItem from="1730187885324" duration="190000" />
      <workItem from="1730188871249" duration="153000" />
      <workItem from="1730189157090" duration="125000" />
      <workItem from="1730273893588" duration="687000" />
      <workItem from="1730274887164" duration="2042000" />
      <workItem from="1730445554248" duration="1183000" />
      <workItem from="1731050535962" duration="639000" />
      <workItem from="1731662998679" duration="1237000" />
      <workItem from="1731917241033" duration="1713000" />
      <workItem from="1732000049312" duration="355000" />
      <workItem from="1732175355086" duration="864000" />
      <workItem from="1733969431528" duration="15000" />
      <workItem from="1735029059042" duration="228000" />
      <workItem from="1735029702516" duration="1461000" />
      <workItem from="1736241430335" duration="908000" />
      <workItem from="1736686653266" duration="3000" />
      <workItem from="1737516722688" duration="109000" />
      <workItem from="1737690358003" duration="1228000" />
      <workItem from="1737711711119" duration="1206000" />
      <workItem from="1737852582733" duration="684000" />
      <workItem from="1738737193955" duration="828000" />
      <workItem from="1738742340545" duration="774000" />
      <workItem from="1739418115084" duration="2596000" />
      <workItem from="1739936485270" duration="2364000" />
      <workItem from="1745460678955" duration="28000" />
      <workItem from="1745460808170" duration="97000" />
      <workItem from="1748509655607" duration="4737000" />
      <workItem from="1748575851462" duration="9864000" />
      <workItem from="1748915792656" duration="836000" />
      <workItem from="1748917281011" duration="1198000" />
      <workItem from="1749002672210" duration="1833000" />
      <workItem from="1749089665036" duration="1221000" />
      <workItem from="1749807095716" duration="940000" />
      <workItem from="1753426824682" duration="8281000" />
      <workItem from="1753673816675" duration="440000" />
      <workItem from="1753685805894" duration="3810000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/ReplaceFrontChart.java</url>
          <line>31</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace.java</url>
          <line>24</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace.java</url>
          <line>27</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace7.java</url>
          <line>22</line>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace9.java</url>
          <line>30</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace14.java</url>
          <line>38</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace14.java</url>
          <line>23</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace20.java</url>
          <line>34</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace27.java</url>
          <line>37</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace27.java</url>
          <line>35</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace33.java</url>
          <line>26</line>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace33.java</url>
          <line>30</line>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace40.java</url>
          <line>34</line>
          <option name="timeStamp" value="33" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace40.java</url>
          <line>47</line>
          <option name="timeStamp" value="35" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-util/src/main/java/pers/xdrodger/util/ExcelDataValidator.java</url>
          <line>70</line>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/practice-demo-util/src/main/java/pers/xdrodger/util/ExcelDataValidator.java</url>
          <line>243</line>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace2.java</url>
          <line>23</line>
          <properties class="pers.xdrodger.simple.test.ppt.poi.practice.TextPartReplace2" method="textPartReplace">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace6.java</url>
          <line>21</line>
          <properties class="pers.xdrodger.simple.test.ppt.poi.practice.TextPartReplace6" method="textPartReplace">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace22.java</url>
          <line>23</line>
          <properties class="pers.xdrodger.simple.test.ppt.poi.practice.TextPartReplace22" method="textPartReplace">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace29.java</url>
          <line>23</line>
          <properties class="pers.xdrodger.simple.test.ppt.poi.practice.TextPartReplace29" method="textPartReplace">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/practice-demo-simple/src/test/java/pers/xdrodger/simple/test/ppt/poi/practice/TextPartReplace39.java</url>
          <line>20</line>
          <properties class="pers.xdrodger.simple.test.ppt.poi.practice.TextPartReplace39" method="textPartReplace">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="JUnit">
        <watch expression="series.getDataLabels().get(2).getTextFrame().getAutofitType()" language="JAVA" />
      </configuration>
      <configuration name="Application">
        <watch expression="cell1.getNumericCellValue()" />
        <watch expression="cell2.getNumericCellValue()" />
      </configuration>
    </watches-manager>
  </component>
</project>