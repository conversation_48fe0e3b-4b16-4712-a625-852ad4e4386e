package pers.xdrodger.simple.test.ppt.apose;

import com.spire.presentation.SlidePicture;
import com.spire.presentation.drawing.IImageData;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;


public class OperationReplaceImageTest {


    @Test
    public void testReplace() throws Exception {
        SpirePrismaPptUtil prismaPptUtil = new SpirePrismaPptUtil(FileUtil.getInputFilePath() + "demo-替换图片.pptx");
        SlidePicture picture = prismaPptUtil.getSlidePicture("replace-image");
        BufferedImage bufferedImage = ImageIO.read( new FileInputStream( FileUtil.getInputFilePath() + "adidas-logo.png" ));
        IImageData imageData = prismaPptUtil.getPpt().getImages().append(bufferedImage);
        picture.getPictureFill().getPicture().setEmbedImage(imageData);
        prismaPptUtil.savePpt(FileUtil.getOutputFilePath() + "demo-替换图片.pptx");
    }
}