package pers.xdrodger.simple.test.ppt.poi.practice;

import javafx.scene.chart.Chart;
import org.apache.poi.xddf.usermodel.chart.XDDFChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xslf.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTDLbls;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTScatterChart;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTScatterSer;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;
import pers.xdrodger.simple.test.ppt.poi.vo.ChartCategorySeriesData;
import pers.xdrodger.simple.test.ppt.poi.vo.PptChartData;

import java.util.ArrayList;
import java.util.List;

import static pers.xdrodger.simple.test.ppt.poi.practice.PrismaPptUtil.FORMAT_CODE_GENERAL;
import static pers.xdrodger.simple.test.ppt.poi.practice.PrismaPptUtil.FORMAT_CODE_NUMBER;


public class UpdateDotFlotChart {


    @Test
    public void updateEeiLineText() throws Exception {
        PrismaPptUtil prismaPptUtil = new PrismaPptUtil(FileUtil.getInputFilePath() + "demo-散点图.pptx");
        XSLFSlide slide = prismaPptUtil.getSlideByShapeName("depart-eei-oci-analysis-chart");
        System.out.println(slide.getSlideNumber());
        XSLFChart chart = prismaPptUtil.getChart(slide,"depart-eei-oci-analysis-chart");
//        CTScatterChart scatterChart = (CTScatterChart) chart.getCTChart().get;
//        List<CTScatterSer> serList = scatterChart.getSerList();
//        for (CTScatterSer ser : serList) {
//            ser.getDPtList();
//        }
        System.out.println(chart.getCTChart().getPlotArea().getScatterChartList().size());
        System.out.println(chart.getCTChart().getPlotArea().getScatterChartList().get(0).getDLbls().getDLblList().size());
        CTScatterChart scatterChart = chart.getCTChart().getPlotArea().getScatterChartList().get(0);
        System.out.println(scatterChart.getDLbls().getDLblList().size());
        List<XDDFChartData> chartDataList = chart.getChartSeries();
        for (XDDFChartData chartData : chartDataList) {
            int seriesCount = chartData.getSeriesCount();
            for (int i =0; i < seriesCount; i ++) {
                XDDFChartData.Series series = chartData.getSeries(i);
                XDDFDataSource xddfDataSource = series.getCategoryData();
                System.out.println(xddfDataSource.getDataRangeReference());
                XDDFNumericalDataSource numericalDataSource = series.getValuesData();
                System.out.println(numericalDataSource.getDataRangeReference());

            }
        }
        List<ChartCategorySeriesData> chartSeriesDataList = new ArrayList<>();
        ChartCategorySeriesData categoryData = new ChartCategorySeriesData();
        categoryData.setName("部门名称");
        categoryData.setFormatCode(FORMAT_CODE_GENERAL);
        List<String> categoryDataList = new ArrayList<>();
        categoryDataList.add("射手座2");
        categoryDataList.add("调研2");
        categoryData.setDataList(categoryDataList);
        chartSeriesDataList.add(categoryData);
        ChartCategorySeriesData xAxisSeriesData = new ChartCategorySeriesData();
        xAxisSeriesData.setName("x轴");
        xAxisSeriesData.setFormatCode(FORMAT_CODE_NUMBER);
        List<Double> xAxisDataList = new ArrayList<>();
        xAxisDataList.add(0.4);
        xAxisDataList.add(0.6);
        xAxisSeriesData.setDataList(xAxisDataList);
        chartSeriesDataList.add(xAxisSeriesData);
        ChartCategorySeriesData yAxisSeriesData = new ChartCategorySeriesData();
        yAxisSeriesData.setName("y轴");
        yAxisSeriesData.setFormatCode(FORMAT_CODE_NUMBER);
        List<Double> yAxisDataList = new ArrayList<>();
        yAxisDataList.add(0.7);
        yAxisDataList.add(0.8);
        yAxisSeriesData.setDataList(yAxisDataList);
        chartSeriesDataList.add(yAxisSeriesData);

        ChartCategorySeriesData tangentLineXAxisSeriesData = new ChartCategorySeriesData();
        tangentLineXAxisSeriesData.setName("切分线");
        tangentLineXAxisSeriesData.setFormatCode(FORMAT_CODE_NUMBER);
        List<Double> tangentLineXAxisDataList = new ArrayList<>();
        tangentLineXAxisDataList.add(0.68);
        tangentLineXAxisSeriesData.setDataList(tangentLineXAxisDataList);
        chartSeriesDataList.add(tangentLineXAxisSeriesData);
        ChartCategorySeriesData tangentLineYAxisSeriesData = new ChartCategorySeriesData();
        tangentLineYAxisSeriesData.setFormatCode(FORMAT_CODE_NUMBER);
        List<Double> tangentLineYAxisDataList = new ArrayList<>();
        tangentLineYAxisDataList.add(0.58);
        tangentLineYAxisSeriesData.setDataList(tangentLineYAxisDataList);
        chartSeriesDataList.add(tangentLineYAxisSeriesData);

        PptChartData pptChartData = new PptChartData();
        pptChartData.setCategorySeriesDataList(chartSeriesDataList);
        pptChartData.setShapeName("depart-eei-oci-analysis-chart");
        prismaPptUtil.replaceChartContent(slide, pptChartData);
        prismaPptUtil.savePpt("demo-散点图.pptx");

    }
}
