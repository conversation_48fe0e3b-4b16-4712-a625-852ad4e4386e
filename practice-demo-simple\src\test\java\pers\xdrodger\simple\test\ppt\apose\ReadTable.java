package pers.xdrodger.simple.test.ppt.apose;

import com.spire.ms.System.Collections.IEnumerator;
import com.spire.presentation.*;
import javafx.scene.control.TreeTableRow;
import org.junit.Test;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;

import java.util.Iterator;

public class ReadTable {

    private ITable getTable(ISlide slide, String tableName) {
        ShapeCollection shapeCollection = slide.getShapes();
        IEnumerator it = shapeCollection.iterator();
        while (it.hasNext()) {
            IShape shape = (IShape) it.next();
            System.out.println(shape.getName());
            if (shape.getName().equals(tableName)) {
                return (ITable) shape;
            }
        }
        return null;
    }

    @Test
    public void testReadTable() throws Exception {
        //实例化一个Presentation对象
        Presentation presentation = new Presentation();

        presentation.loadFromFile(FileUtil.getInputFilePath() + "demo-赞成度表.pptx");
        ISlide slide = presentation.getSlides().get(0);
        ITable table = getTable(slide, "eei-new-table");
        Iterator<TreeTableRow> rowIterator = table.getTableRows().iterator();
        int rowCount = table.getTableRows().getCount();
        int colCount = table.getColumnsList().getCount();
        for (int i = 0; i < rowCount; i ++) {
            for (int j =0; j < colCount; j ++) {
                Cell cell = table.get(j, i);
                System.out.println(cell.getTextFrame().getText());
                if (j ==0 && i == 1) {
                    cell.getTextFrame().setText("敬业度2");
                }
            }
        }
        TableRow row = table.getTableRows().get(rowCount - 1);
        table.getTableRows().append(row);
        TableColumn column =table.getColumnsList().get(colCount-1);
        table.getColumnsList().add(column);
        //保存文件
        presentation.saveToFile(FileUtil.getOutputFilePath() + "demo-赞成度表.pptx", FileFormat.PPTX_2013);
    }

    public static void main(String[] args) throws Exception {




    }

}