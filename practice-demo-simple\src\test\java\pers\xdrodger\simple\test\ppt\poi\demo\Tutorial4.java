package pers.xdrodger.simple.test.ppt.poi.demo;

import java.awt.Color;
import java.awt.Rectangle;
import java.io.FileOutputStream;
import java.io.IOException;
import org.apache.poi.sl.usermodel.TableCell.BorderEdge;
import org.apache.poi.sl.usermodel.TextParagraph.TextAlign;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTable;
import org.apache.poi.xslf.usermodel.XSLFTableCell;
import org.apache.poi.xslf.usermodel.XSLFTableRow;
import org.apache.poi.xslf.usermodel.XSLFTextParagraph;
import org.apache.poi.xslf.usermodel.XSLFTextRun;
import pers.xdrodger.simple.test.ppt.poi.FileUtil;


public class Tutorial4 {
    private Tutorial4() {
    }

    public static void main(String[] args) throws IOException {
        XMLSlideShow ppt = new XMLSlideShow();
        Throwable var2 = null;

        try {
            XSLFSlide slide = ppt.createSlide();
            XSLFTable tbl = slide.createTable();
            tbl.setAnchor(new Rectangle(50, 50, 450, 300));
            int numColumns = 3;
            int numRows = 5;
            XSLFTableRow headerRow = tbl.addRow();
            headerRow.setHeight(50.0D);

            int rownum;
            for(rownum = 0; rownum < numColumns; ++rownum) {
                XSLFTableCell th = headerRow.addCell();
                XSLFTextParagraph p = th.addNewTextParagraph();
                p.setTextAlign(TextAlign.CENTER);
                XSLFTextRun r = p.addNewTextRun();
                r.setText("Header " + (rownum + 1));
                r.setBold(true);
                r.setFontColor(Color.white);
                th.setFillColor(new Color(79, 129, 189));
                th.setBorderWidth(BorderEdge.bottom, 2.0D);
                th.setBorderColor(BorderEdge.bottom, Color.white);
                tbl.setColumnWidth(rownum, 150.0D);
            }

            for(rownum = 0; rownum < numRows; ++rownum) {
                XSLFTableRow tr = tbl.addRow();
                tr.setHeight(50.0D);

                for(int i = 0; i < numColumns; ++i) {
                    XSLFTableCell cell = tr.addCell();
                    XSLFTextParagraph p = cell.addNewTextParagraph();
                    XSLFTextRun r = p.addNewTextRun();
                    r.setText("Cell " + (i + 1));
                    if (rownum % 2 == 0) {
                        cell.setFillColor(new Color(208, 216, 232));
                    } else {
                        cell.setFillColor(new Color(233, 247, 244));
                    }
                }
            }

            FileOutputStream out = new FileOutputStream(FileUtil.getOutputFilePath() + "table.pptx");
            Throwable var41 = null;

            try {
                ppt.write(out);
            } catch (Throwable var35) {
                var41 = var35;
                throw var35;
            } finally {
                if (out != null) {
                    if (var41 != null) {
                        try {
                            out.close();
                        } catch (Throwable var34) {
                            var41.addSuppressed(var34);
                        }
                    } else {
                        out.close();
                    }
                }

            }
        } catch (Throwable var37) {
            var2 = var37;
            throw var37;
        } finally {
            if (ppt != null) {
                if (var2 != null) {
                    try {
                        ppt.close();
                    } catch (Throwable var33) {
                        var2.addSuppressed(var33);
                    }
                } else {
                    ppt.close();
                }
            }

        }
    }
}
