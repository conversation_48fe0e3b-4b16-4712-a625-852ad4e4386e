package pers.xdrodger.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DateUtil;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel数据校验工具类
 * 从第3行第6列开始，校验上下相邻两列数据是否一致
 */
public class ExcelDataValidator {
    
    /**
     * 校验Excel文件数据（默认从第3行第6列开始）
     * @param filePath Excel文件路径
     * @return 校验结果，包含错误信息列表
     */
    public ValidationResult validateData(String filePath) {
        return validateData(filePath, 3, 5); // 默认从第3行第6列开始
    }
    
    /**
     * 校验Excel文件数据
     * @param filePath Excel文件路径
     * @param startRow 起始行索引（从0开始）
     * @param startCol 起始列索引（从0开始）
     * @return 校验结果，包含错误信息列表
     */
    public ValidationResult validateData(String filePath, int startRow, int startCol) {
        List<String> errors = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = createWorkbook(fis, filePath)) {
            
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            
            // 获取总行数
            int lastRowNum = sheet.getLastRowNum();
            
            // 获取总列数
            int lastCellNum = 0;
            if (sheet.getRow(startRow) != null) {
                lastCellNum = sheet.getRow(startRow).getLastCellNum();
            }
            
            // 按对比较，例如第4行与第5行比较，第6行与第7行比较，而第5行与第6行不比较
            for (int i = startRow; i < lastRowNum; i += 2) {
                // 确保有足够的行进行比较
                if (i + 1 > lastRowNum) {
                    break;
                }
                
                Row row1 = sheet.getRow(i);
                Row row2 = sheet.getRow(i + 1);
                
                if (row1 == null || row2 == null) {
                    continue;
                }
                
                // 校验从起始列开始的所有列
                for (int colIndex = startCol; colIndex < lastCellNum; colIndex++) {
                    Cell cell1 = row1.getCell(colIndex);
                    Cell cell2 = row2.getCell(colIndex);
                    
                    if (cell1 == null && cell2 == null) {
                        continue;
                    }
                    
                    // 比较两个单元格的值
                    if (!areCellsEqual(cell1, cell2)) {
                        String value1 = getCellValueAsString(cell1);
                        String value2 = getCellValueAsString(cell2);
                        String errorMsg = String.format("工作表[%s] - 第%d行与第%d行在第%d列的数据不一致: [%s] vs [%s]", 
                            sheet.getSheetName(), i + 1, i + 2, colIndex + 1, value1, value2);
                        errors.add(errorMsg);
                    }
                }
            }
            
        } catch (IOException e) {
            errors.add("读取Excel文件时发生错误: " + e.getMessage());
        } catch (Exception e) {
            errors.add("处理Excel文件时发生错误: " + e.getMessage());
        }
        
        return new ValidationResult(errors.isEmpty(), errors);
    }
    
    /**
     * 校验Excel文件中以指定名称开头的工作表数据
     * @param filePath Excel文件路径
     * @param sheetNamePrefix 工作表名称前缀
     * @param startRow 起始行索引（从0开始）
     * @param startCol 起始列索引（从0开始）
     * @return 校验结果，包含错误信息列表
     */
    public ValidationResult validateDataBySheetNamePrefix(String filePath, String sheetNamePrefix, int startRow, int startCol) {
        List<String> errors = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = createWorkbook(fis, filePath)) {
            
            // 查找以指定前缀开头的工作表
            Sheet sheet = null;
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet s = workbook.getSheetAt(i);
                if (s.getSheetName().startsWith(sheetNamePrefix)) {
                    sheet = s;
                    break;
                }
            }
            
            // 如果没有找到匹配的工作表，添加错误信息
            if (sheet == null) {
                errors.add("未找到以 '" + sheetNamePrefix + "' 开头的工作表");
                return new ValidationResult(false, errors);
            }
            
            // 获取总行数
            int lastRowNum = sheet.getLastRowNum();
            
            // 获取总列数
            int lastCellNum = 0;
            if (sheet.getRow(startRow) != null) {
                lastCellNum = sheet.getRow(startRow).getLastCellNum();
            }
            
            // 按对比较，例如第4行与第5行比较，第6行与第7行比较，而第5行与第6行不比较
            for (int i = startRow; i < lastRowNum; i += 2) {
                // 确保有足够的行进行比较
                if (i + 1 > lastRowNum) {
                    break;
                }
                
                Row row1 = sheet.getRow(i);
                Row row2 = sheet.getRow(i + 1);
                
                if (row1 == null || row2 == null) {
                    continue;
                }
                
                // 校验从起始列开始的所有列
                for (int colIndex = startCol; colIndex < lastCellNum; colIndex++) {
                    Cell cell1 = row1.getCell(colIndex);
                    Cell cell2 = row2.getCell(colIndex);
                    
                    if (cell1 == null && cell2 == null) {
                        continue;
                    }
                    
                    // 比较两个单元格的值
                    if (!areCellsEqual(cell1, cell2)) {
                        String value1 = getCellValueAsString(cell1);
                        String value2 = getCellValueAsString(cell2);
                        String errorMsg = String.format("工作表[%s] - 第%d行与第%d行在第%d列的数据不一致: [%s] vs [%s]", 
                            sheet.getSheetName(), i + 1, i + 2, colIndex + 1, value1, value2);
                        errors.add(errorMsg);
                    }
                }
            }
            
        } catch (IOException e) {
            errors.add("读取Excel文件时发生错误: " + e.getMessage());
        } catch (Exception e) {
            errors.add("处理Excel文件时发生错误: " + e.getMessage());
        }
        
        return new ValidationResult(errors.isEmpty(), errors);
    }
    
    /**
     * 根据文件扩展名创建相应的Workbook对象
     */
    private Workbook createWorkbook(FileInputStream fis, String filePath) throws IOException {
        if (filePath.endsWith(".xlsx")) {
            return new XSSFWorkbook(fis);
        } else if (filePath.endsWith(".xls")) {
            return new HSSFWorkbook(fis);
        } else {
            throw new IllegalArgumentException("不支持的文件格式: " + filePath);
        }
    }
    
    /**
     * 获取单元格的字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case BLANK:
                return "";
            default:
                return cell.toString();
        }
    }

    /**
     * 比较两个单元格的值是否相等
     */
    private boolean areCellsEqual(Cell cell1, Cell cell2) {
        // 如果其中一个为null，则必须两个都为null才相等
        if (cell1 == null || cell2 == null) {
            return cell1 == cell2;
        }
        
        // 获取单元格类型并比较值
        CellType type1 = cell1.getCellType();
        CellType type2 = cell2.getCellType();
        
        // 类型不同则不相等
        if (type1 != type2) {
            return false;
        }
        
        // 根据类型比较值
        switch (type1) {
            case STRING:
                return cell1.getStringCellValue().equals(cell2.getStringCellValue());
            case NUMERIC:
                // 处理日期和数字
                if (DateUtil.isCellDateFormatted(cell1) && DateUtil.isCellDateFormatted(cell2)) {
                    return cell1.getDateCellValue().equals(cell2.getDateCellValue());
                } else {
                    return cell1.getNumericCellValue() == cell2.getNumericCellValue();
                }
            case BOOLEAN:
                return cell1.getBooleanCellValue() == cell2.getBooleanCellValue();
            case BLANK:
                return true; // 两个空白单元格视为相等
            default:
                return false; // 其他类型视为不相等
        }
    }
    
    /**
     * 校验结果类
     */
    public static class ValidationResult {
        private boolean valid;
        private List<String> errors;
        
        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public List<String> getErrors() {
            return errors;
        }
    }

    /**
     * 校验Excel文件中以"数据概览表"开头的工作表数据
     * 校验所有以"数据概览表"开头的工作表，找到第一行值为"VS历史"的列，
     * 然后校验该列从第四行开始的值是否都为0
     * @param filePath Excel文件路径
     * @return 校验结果，包含错误信息列表
     */
    public ValidationResult validateDataOverviewSheet(String filePath) {
        List<String> errors = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = createWorkbook(fis, filePath)) {
            
            // 查找所有以"数据概览表"开头的工作表
            List<Sheet> sheets = new ArrayList<>();
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                if (sheet.getSheetName().startsWith("数据概览表")) {
                    sheets.add(sheet);
                }
            }
            
            // 如果没有找到匹配的工作表，添加错误信息
            if (sheets.isEmpty()) {
                errors.add("未找到以 '数据概览表' 开头的工作表");
                return new ValidationResult(false, errors);
            }
            
            // 校验每个匹配的工作表
            for (Sheet sheet : sheets) {
                // 找到第一行值为"VS历史"的列
                Row firstRow = sheet.getRow(0);
                if (firstRow == null) {
                    errors.add("工作表[" + sheet.getSheetName() + "] - 第一行为空");
                    continue;
                }
                
                int targetColumnIndex = -1;
                for (int i = 0; i < firstRow.getLastCellNum(); i++) {
                    Cell cell = firstRow.getCell(i);
                    if (cell != null && "VS历史".equals(getCellValueAsString(cell))) {
                        targetColumnIndex = i;
                        break;
                    }
                }
                
                // 如果没有找到"VS历史"列，添加错误信息
                if (targetColumnIndex == -1) {
                    errors.add("工作表[" + sheet.getSheetName() + "] - 未找到'VS历史'列");
                    continue;
                }
                
                // 校验目标列从第四行开始的值是否都为0
                int lastRowNum = sheet.getLastRowNum();
                for (int rowIndex = 3; rowIndex <= lastRowNum; rowIndex++) {
                    Row row = sheet.getRow(rowIndex);
                    if (row != null) {
                        Cell cell = row.getCell(targetColumnIndex);
                        if (cell != null) {
                            // 检查单元格值是否为0
                            boolean isZero = false;
                            switch (cell.getCellType()) {
                                case NUMERIC:
                                    isZero = cell.getNumericCellValue() == 0.0;
                                    break;
                                case STRING:
                                    isZero = "0".equals(cell.getStringCellValue().trim());
                                    break;
                                case BLANK:
                                    // 空白单元格视为0
                                    isZero = true;
                                    break;
                                default:
                                    // 其他类型不视为0
                                    isZero = false;
                                    break;
                            }
                            
                            if (!isZero) {
                                String cellValue = getCellValueAsString(cell);
                                String errorMsg = String.format("工作表[%s] - 第%d行第%d列的值必须是0，当前值为: %s", 
                                    sheet.getSheetName(), rowIndex + 1, targetColumnIndex + 1, cellValue);
                                errors.add(errorMsg);
                            }
                        }
                    }
                }
            }
            
        } catch (IOException e) {
            errors.add("读取Excel文件时发生错误: " + e.getMessage());
        } catch (Exception e) {
            errors.add("处理Excel文件时发生错误: " + e.getMessage());
        }
        
        return new ValidationResult(errors.isEmpty(), errors);
    }

    public static void main(String[] args) {
        String fileName ="C:\\Users\\<USER>\\Downloads\\马格南_维度得分表_202507251628\\组织敬业度调研_中粮资本控股股份有限公司-中英人寿_zl6_敬满_202507251628.xlsx";
        ExcelDataValidator validator = new ExcelDataValidator();
        ValidationResult validationResult = validator.validateDataBySheetNamePrefix(fileName, "数据汇总表", 3, 5);
        validationResult.errors.forEach(System.out::println);
        ValidationResult validationResult2 = validator.validateDataOverviewSheet(fileName);
        validationResult2.errors.forEach(System.out::println);
    }
}
